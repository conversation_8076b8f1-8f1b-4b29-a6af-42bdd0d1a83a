<?php

if (! function_exists('trans_config')) {
    function trans_config($key, $default = null) {
        try {
            if (app()->has('translator')) {
                $translated = __($key);
                $text = $translated !== $key ? $translated : ($default ?? $key);
                return str_replace(':brandName', config('brand.name'), $text);
            }
            return $key;
        } catch (Exception $e) {
            return $key;
        }
    }
}

if (! function_exists('translate_text_with_config')) {
    function translate_text_with_config($configs) {
        foreach ($configs as $key => $config) {
            if (is_array($config)) {
                $configs[$key] = translate_text_with_config($config);
            } else {
                $configs[$key] = __($config);
            }
        }
        return $configs;
    }
}