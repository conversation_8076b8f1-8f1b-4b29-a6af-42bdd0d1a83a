<?php

if (! function_exists('trans_config')) {
    function trans_config($key, $default = null) {
        try {
            if (app()->has('translator')) {
                $translated = __($key);
                // Nếu translation key kh<PERSON><PERSON> tồ<PERSON> t<PERSON>, <PERSON><PERSON> sẽ trả về key gốc
                return $translated !== $key ? $translated : ($default ?? $key);
            }
            return $default ?? $key;
        } catch (Exception $e) {
            return $default ?? $key;
        }
    }
}