<?php

namespace App\Http\Controllers\GameController;

use App\Enums\Common;
use App\Enums\GatewayEndpoint;
use App\Enums\UrlPathEnum;
use App\Helpers\DetectDeviceHelper;

use function App\Helpers\generateJsonLd;
use function App\Helpers\generateSeoMetaData;

use App\Http\Controllers\Controller;
use App\Services\GameService;
use App\Services\GatewayApi;
use App\Traits\GameFilterTrait;
use GuzzleHttp\Promise\Utils;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GameController extends Controller
{
    use GameFilterTrait;

    // Api gateway service
    protected $GatewayApi;
    private $gameService;

    public function __construct(GatewayApi $GatewayApi, GameService $gameService)
    {
        $this->GatewayApi = $GatewayApi;
        $this->gameService = $gameService;
    }

    public function index(Request $request, $type = null)
    {

        //Get games
        if (!$type) {
            $type = Common::TYPE_ALL->value;
        } elseif ($type && $type !== 'favorite') {
            $request->merge(['type' => $type]);
        } elseif ($type === 'favorite') {
            $request->merge(['sort' => $type]);
        }
        
        // if the request is made via js, return only the game list
        if ($request->ajax() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
            $response = $this->getGames($request, $this->GatewayApi, GatewayEndpoint::GAME_SEARCH->value);
            return $response;
        }

        $promises = [
            'gamesRes' => $this->getGames($request, $this->GatewayApi, GatewayEndpoint::GAME_SEARCH->value),
            'gameType' => $this->gameService->getGameTypes($request),
        ];

        $responses = Utils::all($promises)->wait();

        $gamesRes = $responses['gamesRes'];
        $gameType = $responses['gameType'];

        $games = !empty($gamesRes->data->items) ? $gamesRes->data->items : [];
        $gamesTotal = !empty($gamesRes->data->total) ? $gamesRes->data->total : 0;
        
        $filterKey = 'gameFilter';
        $prefixLink = UrlPathEnum::GAME->value;
        $activeFilter = $this->getActiveFilter($request);

        $filters = $this->getFilters($filterKey, $type, $gameType, $prefixLink);
        $swiperConfig = config('constants.swiperConfig');
        
        // config SEO
        $breadCrumbText = isset(config('games.breadcrumbText')[$type]) ? __(config('games.breadcrumbText')[$type]) : __('common.games');
        $seoGamePath = [
            'all' => 'games',
            'quay-so-number-games' => 'slots',
            'keno' => 'keno',
            'no-hu' => 'nohu',
            'game-bai' => 'game-cards',
            'xo-so' => 'games',
            'ban-ca' => 'fishing',
            'quay-slots' => 'quay-slots',
            'table-games' => 'table-games',
            'game-nhanh' => 'instant-games',
            'game-khac' => 'games-other',
        ];
        $seo = generateSeoMetaData(isset($seoGamePath[$type]) ? $seoGamePath[$type] : 'games');

        $seo->schemas = [

            generateJsonLd('WebSite', [
                'name' => $seo->title,
                'alternateName' => '',
                'url' => url()->current(),
                'description' => $seo->description,
                'potentialAction' => [
                    '@type' => '',
                    'target' => '',
                    'query-input' => '',
                ],
            ]),

            generateJsonLd('BreadcrumbList', [
                'itemListElement' => [
                    [
                        '@type' => 'ListItem',
                        'position' => '1',
                        'item' => [
                            '@id' => route('en.home.index'),
                            'name' => __('pages.navigation.home'),
                        ],
                    ],
                    [
                        '@type' => 'ListItem',
                        'position' => '2',
                        'item' => [
                            '@id' => route('en.games.index'),
                            'name' =>  __('common.games'),
                        ],
                    ],
                    [
                        '@type' => 'ListItem',
                        'position' => '3',
                        'item' => [
                            '@id' => $request->url(),
                            'name' => $type === 'all' ? __('pages.common.all') : $breadCrumbText,
                        ],
                    ],
                ],
            ]),

            generateJsonLd('Product', [
                'name' => $seo->title,
                'image' => '',
                'description' => $seo->description,
                'sku' => '',
                'brand' => [
                    '@type' => 'Brand',
                    'name' => '',
                ],
                'aggregateRating' => [
                    '@type' => 'AggregateRating',
                    'ratingValue' => '4.8',
                    'reviewCount' => '288',
                    'bestRating' => '5',
                ],
            ]),
        ];

        $isValidUrl = $this->checkIsValidGameType($type, $gameType, '/cong-game');
        abort_if(!$isValidUrl, 404);

        return view('pages.games', [
            'games' => $games,
            'gamesTotal' => $gamesTotal,
            'activeFilter' => $activeFilter,
            'filters' => $filters,
            'routeUrl' => $request->url(),
            'swiperConfig' => $swiperConfig,
            'breadCrumbText' => $breadCrumbText,
        ]);
    }

    public function gameUrl(Request $request)
    {
        return $this->gameService->gameUrl($request);
    }

    public function cockfight(Request $request, $slug = null)
    {
        if (!Auth::check()) {
            return redirect('/');
        }

        // config SEO
        $seo = generateSeoMetaData('games');
        $seo->schemas = [

            generateJsonLd('WebSite', [
                'name' => $seo->title,
                'alternateName' => '',
                'url' => url()->current(),
                'description' => $seo->description,
                'potentialAction' => [
                    '@type' => '',
                    'target' => '',
                    'query-input' => '',
                ],
            ]),

            generateJsonLd('BreadcrumbList', [
                'itemListElement' => [
                    [
                        '@type' => 'ListItem',
                        'position' => '1',
                        'item' => [
                            '@id' => route('en.home.index'),
                            'name' => __('pages.navigation.home'),
                        ],
                    ],
                    [
                        '@type' => 'ListItem',
                        'position' => '2',
                        'item' => [
                            '@id' => route('en.games.index'),
                            'name' => __('common.games'),
                        ],
                    ],
                ],
            ]),

            generateJsonLd('Product', [
                'name' => $seo->title,
                'image' => '',
                'description' => $seo->description,
                'sku' => '',
                'brand' => [
                    '@type' => 'Brand',
                    'name' => '',
                ],
                'aggregateRating' => [
                    '@type' => 'AggregateRating',
                    'ratingValue' => '4.8',
                    'reviewCount' => '288',
                    'bestRating' => '5',
                ],
            ]),
        ];

        $iframeUrl = $this->getGameUrl($request, $slug, $this->GatewayApi);
        return view('pages.game-iframe', [
            'iframeUrl' => $iframeUrl || '',
            'isMobile' => $request->get('isMobile'),
        ]);
    }

    public function getLoDeSieuToc(Request $request)
    {
        // config SEO
        $seo = generateSeoMetaData('lo-de-sieu-toc');
        $seo->schemas = [

            generateJsonLd('WebSite', [
                'name' => $seo->title,
                'alternateName' => '',
                'url' => url()->current(),
                'description' => $seo->description,
                'potentialAction' => [
                    '@type' => '',
                    'target' => '',
                    'query-input' => '',
                ],
            ]),

            generateJsonLd('BreadcrumbList', [
                'itemListElement' => [],
            ]),
        ];

        $iframeUrl = $this->getGameUrl($request, 'lo-de-sieu-toc', $this->GatewayApi);
        return view('pages.game-iframe', [
            'iframeUrl' => $iframeUrl || '',
            'isMobile' => $request->get('isMobile'),
        ]);
    }

    public function getLode3mien(Request $request)
    {
        // config SEO
        $seo = generateSeoMetaData('lo-de-ba-mien');
        $seo->schemas = [

            generateJsonLd('WebSite', [
                'name' => $seo->title,
                'alternateName' => '',
                'url' => url()->current(),
                'description' => $seo->description,
                'potentialAction' => [
                    '@type' => '',
                    'target' => '',
                    'query-input' => '',
                ],
            ]),

            generateJsonLd('BreadcrumbList', [
                'itemListElement' => [],
            ]),
        ];

        $iframeUrl = $this->getGameUrl($request, 'lode', $this->GatewayApi);
        return view('pages.lode3mien', [
            'iframeUrl' => $iframeUrl || '',
            'token' => Auth::check() && isset(Auth::user()->tp_token) && Auth::user()->tp_token ? Auth::user()->tp_token : '',
            'isMobile' => $request->get('isMobile'),
        ]);
    }

    public function getMega645(Request $request)
    {
        // config SEO
        $seo = generateSeoMetaData();
        $seo->schemas = [

            generateJsonLd('WebSite', [
                'name' => $seo->title,
                'alternateName' => '',
                'url' => url()->current(),
                'description' => $seo->description,
                'potentialAction' => [
                    '@type' => '',
                    'target' => '',
                    'query-input' => '',
                ],
            ]),

            generateJsonLd('BreadcrumbList', [
                'itemListElement' => [],
            ]),
        ];
        $token = Auth::check() && isset(Auth::user()->tp_token) && Auth::user()->tp_token ?? '';

        return view('pages.mega645', [
            'token' => $token,
            'isMobile' => $request->get('isMobile'),
        ]);
    }

    public function getPower655(Request $request)
    {
        // config SEO
        $seo = generateSeoMetaData();
        $seo->schemas = [

            generateJsonLd('WebSite', [
                'name' => $seo->title,
                'alternateName' => '',
                'url' => url()->current(),
                'description' => $seo->description,
                'potentialAction' => [
                    '@type' => '',
                    'target' => '',
                    'query-input' => '',
                ],
            ]),

            generateJsonLd('BreadcrumbList', [
                'itemListElement' => [],
            ]),
        ];

        $token = Auth::check() && isset(Auth::user()->tp_token) && Auth::user()->tp_token ?? '';


        return view('pages.power655', [
            'token' => $token,
            'isMobile' => $request->get('isMobile'),
        ]);
    }

    public function getMd5(Request $request)
    {
        // config SEO
        $seo = generateSeoMetaData();
        $seo->schemas = [

            generateJsonLd('WebSite', [
                'name' => $seo->title,
                'alternateName' => '',
                'url' => url()->current(),
                'description' => $seo->description,
                'potentialAction' => [
                    '@type' => '',
                    'target' => '',
                    'query-input' => '',
                ],
            ]),

            generateJsonLd('BreadcrumbList', [
                'itemListElement' => [],
            ]),
        ];

        $token = Auth::check() && isset(Auth::user()->tp_token) && Auth::user()->tp_token ?? '';


        return view('pages.md5', [
            'token' => $token,
            'isMobile' => $request->get('isMobile'),
        ]);
    }

    public function getListLode(Request $request)
    {
        generateSeoMetaData('lode');

        $isMobile = DetectDeviceHelper::isMobile();
        abort_if(!$isMobile, 404);

        $gameList = config('lode.listLode');
        return view('pages.games', [
            'routeUrl' => $request->url(),
            'games' => $gameList,
            'hasFavorite' => false,
            'breadcrumb' => __('pages.navigation.lottery'),
        ]);
    }

    public function getListDaga(Request $request)
    {
        generateSeoMetaData('daga');

        $isMobile = DetectDeviceHelper::isMobile();
        abort_if(!$isMobile, 404);

        $dagaConfig = config('daga');

        $gameList = [];

        //Get da ga data
        $dagaParams = ['limit' => 2, 'type' => 'da-ga', 'page' => 1];
        $response = $this->gameService->getGameByParams($request, $dagaParams);
        $games = !empty($response->items) ? $response->items : [];

        foreach ($games as $item) {
            $key = strtolower($item->partner_provider);

            if (in_array($key, ['ga28', 'ws168'])) {

                $daGaItem =  $dagaConfig[$key];
                $daGaItem['isFavorite'] = $item->is_favorite;
                $daGaItem['title'] = $item->name;
                $daGaItem['partnerGameId'] = $item->partner_game_id;
                $daGaItem['partner'] = $item->partner;
                $gameItem['apiUrl'] = $item->api_url;

                array_push($gameList, $daGaItem);
            }
        }

        return view('pages.games', [
            'routeUrl' => $request->url(),
            'games' => $gameList,
            'hasFavorite' => true,
            'breadcrumb' => __('pages.navigation.cockfighting'),
        ]);
    }

    public function getQuaySoList(Request $request)
    {
        generateSeoMetaData('quayso');

        $isMobile = DetectDeviceHelper::isMobile();
        abort_if(!$isMobile, 404);

        $quaysoConfig = config('quayso');

        $gameList = [];

        //Get quay so data
        $dagaParams = ['limit' => 20, 'type' => 'lottery', 'page' => 1];
        $response = $this->gameService->getGameByParams($request, $dagaParams);
        $games = !empty($response->items) ? $response->items : [];

        foreach ($quaysoConfig as $key => $quaysoItem) {
            // Store filteredGames in a variable
            $filteredGames = array_filter($games, function ($value) use ($key) {
                return $value->partner_game_id == $key;
            });

            // reset the variable
            $item = reset($filteredGames);

            if ($item) {
                $gameItem = $quaysoItem;
                $gameItem['isFavorite'] = $item->is_favorite;
                $gameItem['partnerGameId'] = $item->partner_game_id;
                $gameItem['partner'] = $item->partner;
                $gameItem['apiUrl'] = $item->api_url;
                array_push($gameList, $gameItem);
            }
        }

        return view('pages.games', [
            'routeUrl' => $request->url(),
            'games' => $gameList,
            'hasFavorite' => true,
            'breadcrumb' => __('pages.navigation.number_games'),
            'hasOpenGame' => true,
        ]);
    }

    public function getQuaySo(Request $request, $slug)
    {

        // config SEO
        $seo = generateSeoMetaData('games');

        $iframeUrl = $this->getGameUrl($request, $slug, $this->GatewayApi);
        return view('pages.game-iframe', [
            'iframeUrl' => $iframeUrl || '',
            'isMobile' => $request->get('isMobile'),
        ]);
    }
}
