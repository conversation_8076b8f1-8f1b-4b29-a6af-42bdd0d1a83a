<?php


return [
    'deposit' => [
        'promotions_desc' => [
            [
                'label' => trans_config('pages.account.promotion', 'Khuyến mãi'),
                'desc' => '500,000 VND',
            ],
            [
                'label' => trans_config('pages.account.actual_received', 'Thực nhận'),
                'desc' => '1,000,000 VND',
            ],
            [
                'label' => trans_config('pages.account.bet_rounds', 'Số vòng cược'),
                'desc' => '25 Vòng',
            ],
            [
                'label' => trans_config('pages.account.required_bet_amount', 'Tiền cược yêu cầu'),
                'desc' => '25,000 K',
            ],
        ],
        'tabs' => [
            [
                'id' => 'p2p',
                'title' => trans_config('pages.account.p2p_transaction', 'giao dịch P2P'),
                'icon' => 'icon-p2p',
                'description' => 'P2P',
                'is_active' => true,
                'status_text' => trans_config('pages.account.status_new', 'mới'),
                'status' => 'new',
            ],
            [
                'id' => 'codepay',
                'title' => 'Codepay',
                'icon' => 'icon-quick-deposit',
                'description' => 'Codepay',
                'is_active' => true,
                'status_text' => trans_config('pages.account.status_recommended', 'đề xuất'),
                'status' => 'propose',
            ],
            [
                'id' => 'crypto',
                'title' => trans_config('pages.account.crypto', 'tiền ảo'),
                'icon' => 'icon-crypto',
                'description' => trans_config('pages.account.crypto', 'tiền ảo'),
                'is_active' => true,
                'status_text' => trans_config('pages.account.status_new', 'mới'),
                'status' => 'new',
            ],
            [
                'id' => 'ewallet',
                'title' => trans_config('pages.account.ewallet', 'Ví điện tử'),
                'icon' => 'icon-wallet',
                'description' => trans_config('pages.account.ewallet', 'Ví điện tử'),
                'is_active' => true,
                'status_text' => '',
                'status' => '',
            ],
            [
                'id' => 'card',
                'title' => trans_config('pages.account.phone_card', 'Thẻ cào'),
                'icon' => 'icon-phone-card',
                'description' => trans_config('pages.account.phone_card', 'Thẻ cào'),
                'is_active' => true,
                'status' => '',
                'status_text' => '',
            ],
        ],
    ],
    'withdraw_tabs' => [
        [
            'id' => 'p2p',
            'title' => 'giao dịch P2P',
            'icon' => 'icon-p2p',
            'description' => 'P2P',
            'is_active' => true,
            'status_text' => 'mới',
            'status' => 'new',
        ],
        // [
        //     'id' => 'coin12',
        //     'title' => 'Coin12',
        //     'icon' => 'icon-coin12',
        //     'description' => 'Coin12',
        //     'is_active' => true,
        //     'status' => 'maintain'
        // ],
        [
            'id' => 'bank',
            'title' => 'Ngân hàng',
            'icon' => 'icon-bank',
            'description' => 'Ngân hàng',
            'is_active' => true,
        ],
        [
            'id' => 'crypto',
            'title' => 'tiền ảo',
            'icon' => 'icon-crypto',
            'description' => 'tiền ảo',
            'is_active' => true,
        ],
        [
            'id' => 'card',
            'title' => 'Thẻ cào',
            'icon' => 'icon-phone-card',
            'description' => 'Thẻ cào',
            'is_active' => true,
        ],
    ],
    'overview' => [
        'id' => 'overview',
        'href' => '/account',
        'icon' => 'vendor/accounts/images/account/menu/overview.svg',
        'name' => 'account.menus.overview_title',
        'active_list' => [],
    ],
    'transaction' => [
        [
            'id' => 'deposit',
            'href' => '/account/deposit',
            'icon' => 'vendor/accounts/images/account/menu/deposit.svg',
            'icon_active' => 'vendor/accounts/images/account/menu/deposit-active.svg',
            'name' => 'account.menus.deposit',
            'name_mb' => 'account.menus.deposit',
            'active_list' => [
                '/account/deposit/p2p',
                '/account/deposit/codepay',
                '/account/deposit/crypto',
                '/account/deposit/ewallet',
                '/account/deposit/card',
            ],
        ],
        [
            'id' => 'withdraw',
            'href' => '/account/withdraw',
            'icon' => 'vendor/accounts/images/account/menu/withdraw.svg',
            'icon_active' => 'vendor/accounts/images/account/menu/withdraw-active.svg',
            'name' => 'account.menus.withdraw',
            'name_mb' => 'account.menus.withdraw',
            'active_list' => [
                '/account/withdraw/coin12',
                '/account/withdraw/bank',
                '/account/withdraw/crypto',
                '/account/withdraw/card',
            ],
        ],
        [
            'id' => 'bank-management',
            'href' => '/account/bank-account',
            'icon' => 'vendor/accounts/images/account/menu/bank.svg',
            'icon_active' => 'vendor/accounts/images/account/menu/bank-active.svg',
            'name' => 'account.menus.bank_management',
            'name_mb' => 'account.menus.bank_management_mb',
            'active_list' => [],
        ],
        [
            'id' => 'history',
            'href' => '/account/history',
            'icon' => 'vendor/accounts/images/account/menu/history.svg',
            'icon_active' => 'vendor/accounts/images/account/menu/history-active.svg',
            'name' => 'account.menus.history',
            'name_mb' => 'account.menus.history_mb',
            'active_list' => [],
        ],
    ],
    'more' => [
        [
            'id' => 'personal_information',
            'href' => '/account/information',
            'icon' => 'vendor/accounts/images/account/menu/account.svg',
            'icon_active' => 'vendor/accounts/images/account/menu/account-active.svg',
            'name' => 'account.menus.personal_information',
            'active_list' => [],
        ],
        [
            'id' => 'promotion',
            'href' => '/account/promotion',
            'icon' => 'vendor/accounts/images/account/menu/promotion.svg',
            'icon_active' => 'vendor/accounts/images/account/menu/promotion-active.svg',
            'name' => 'account.menus.promotion',
            'active_list' => [],
        ],
    ],
    'information' => [
        [
            'id' => 'personal_information',
            'href' => '/account/information',
            'icon' => 'vendor/accounts/images/account/menu/profile.svg',
            'name' => 'account.menus.personal_information',
        ],
    ],
    'history' => [
        'tabList' => [
            [
                'label' => trans_config('pages.account.bet_history', 'Lịch Sử Cược'),
                'value' => 'bet',
                'key' => 'betting',
            ],
            [
                'label' => trans_config('pages.account.transaction_history', 'Lịch Sử Giao Dịch'),
                'value' => 'transaction',
                'key' => 'transaction',
            ],
        ],
        'betHeader' => [
            trans_config('pages.account.transaction_id', 'Mã giao dịch'),
            trans_config('pages.account.time', 'Thời gian'),
            trans_config('pages.account.game', 'Game'),
            trans_config('pages.account.amount', 'Số tiền'),
            trans_config('pages.account.win_lose', 'Thắng/Thua'),
            trans_config('pages.account.turnover', 'Turnover'),
            trans_config('pages.account.status', 'Trạng thái')
        ],
        'transactionHeader' => [
            trans_config('pages.account.time', 'Thời gian'),
            trans_config('pages.account.transaction', 'Giao dịch'),
            trans_config('pages.account.bank', 'Ngân hàng'),
            trans_config('pages.account.method', 'Phương thức'),
            trans_config('pages.account.amount', 'Số tiền'),
            trans_config('pages.account.transaction_id', 'Mã giao dịch'),
            trans_config('pages.account.status', 'Trạng thái')
        ],
        'statusBet' => [
            '' => 'Tất cả',
            'ALL' => 'Tất cả',
            'WIN' => 'Thắng',
            'WON' => 'Thắng',
            'LOSE' => 'Thua',
            'LOST' => 'Thua',
            'CANCEL' => 'Huỷ',
            'DRAW' => 'Hòa',
            'TIP' => 'Tip',
            'TIPS' => 'Tip',
            'RUNING' => 'Đang diễn ra',
            'RUNNING' => 'Đang diễn ra',
            'PENDING' => 'Đang chờ',
            'OPENED' => 'Đang diễn ra',
            'HALF LOSE' => 'Thua 1/2',
            'HALF WON' => 'Thắng 1/2',
            'HALF_LOSE' => 'Thua 1/2',
            'HALF_WON' => 'Thắng 1/2',
            'BONUS' => 'Bonus',
            'BET' => 'Đang chờ',
        ],
        'typeTransaction' => [
            'WITHDRAW' => trans_config('pages.account.withdraw', 'Rút tiền'),
            'DEPOSIT' => trans_config('pages.account.deposit', 'Nạp tiền'),
        ],
        'methodTransaction' => [
            'ibanking' => trans_config('pages.account.bank', 'Ngân hàng'),
            'bank_account' => trans_config('pages.account.bank_transfer', 'Chuyển khoản'),
            'nicepay' => 'Codepay',
            'phone_card' => trans_config('pages.account.phone_card', 'Thẻ cào'),
            'crypto' => trans_config('pages.account.crypto', 'Crypto'),
            'daily_cashback_slot' => trans_config('pages.account.slots_cashback', 'Hoàn trả Slots'),
        ],
        'statusTransaction' => [
            'CANCEL' => 'Thất bại',
            'DRAFT' => 'Đang xử lý',
            'FINISHED' => 'Hoàn thành',
            'APPROVED' => 'Đang xử lý',
            'WAITING' => 'Đang xử lý',
            'PENDING' => 'Đang xử lý',
            'PROCESSING' => 'Đang xử lý',
            'PHONE_CARD_PROCESSING' => 'Đang xử lý',
            'PHONE_CARD_PENDING' => 'Đang xử lý',
            'PHONE_CARD_FINISHED' => 'Hoàn thành',
            'PHONE_CARD_CANCEL' => 'Thất bại',
            'PHONE_CARD_DRAFT' => 'Đang xử lý',
        ],
        'bankList' => [
            'MSB' => 'Maritime Bank',
            'VIETBANK' => 'VietBank',
            'DAB' => 'Vikki Digital Bank',
        ],
    ],
    'guide_crypto' => [
        [
            'name' => 'Binance',
            'image' => 'binance',
        ],
        [
            'name' => 'Coin12',
            'image' => 'coin12',
        ],
        [
            'name' => 'Huobi',
            'image' => 'huobi',
        ],
        [
            'name' => 'Remitano',
            'image' => 'remitano',
        ],
    ],
    'accountPageSpecial' => [
        'account/bank-account',
    ],
];
