<?php

use App\Enums\UrlPathEnum;

return [
    'navList' => [
        [
            'href' => UrlPathEnum::SPORTS->value,
            'labelType' => null,
            'label' => __('pages.navigation.sports'),
            'activeList' => [
                '/ca-do-bong-da',
                '/ca-do-bong-da/ksports',
                '/ca-do-bong-da/saba-sports',
                '/ca-do-bong-da/bti-sports',
                '/ca-do-bong-da/t-sports',
                '/ca-do-bong-da/virtual-im-sports',
                '/ca-do-bong-da/virtual-k-sports',
                '/ca-do-bong-da/virtual-saba-sports',
                '/ca-do-bong-da/virtual-pp-sports',
            ],
            'icon' => 'asset/images/header/icon/sports.svg',
        ],
        [
            'href' => UrlPathEnum::CASINO->value,
            'labelType' => 'live',
            'label' => __('pages.navigation.casino'),
            'activeList' => [
                '/song-bai-livecasino-truc-tuyen',
                '/song-bai-livecasino-truc-tuyen/all',
                '/song-bai-livecasino-truc-tuyen/favorite',
                '/song-bai-livecasino-truc-tuyen/sexy',
                '/song-bai-livecasino-truc-tuyen/tai-xiu',
                '/song-bai-livecasino-truc-tuyen/baccarat',
                '/song-bai-livecasino-truc-tuyen/blackjack',
                '/song-bai-livecasino-truc-tuyen/roulette',
                '/song-bai-livecasino-truc-tuyen/poker',
                '/song-bai-livecasino-truc-tuyen/xoc-dia',
                '/song-bai-livecasino-truc-tuyen/rong-ho',
                '/song-bai-livecasino-truc-tuyen/other',
                '/song-bai-livecasino-truc-tuyen/game-khac',
            ],
            'icon' => 'asset/images/header/icon/casino.svg',
        ],
        [
            'href' => UrlPathEnum::NOHU->value,
            'labelType' => null,
            'label' => __('pages.navigation.jackpot'),
            'icon' => 'asset/images/header/icon/slots.svg',
        ],
        [
            'href' => '',
            'labelType' => 'hot',
            'label' => __('pages.navigation.number_games'),
            'checkAuth' => false,
            'activeList' => [
                '/quayso',
                '/quayso/quayso1',
                '/quayso/quayso5',
                '/quayso/quayso2',
                '/quayso/atom',
                '/quayso/numbergame1',
                '/quayso/numbergame2',
            ],
            'icon' => 'asset/images/header/icon/quayso.svg',
            'list' => [
                [
                    'icon' => '/asset/images/header/atom.avif',
                    'label' => 'atom',
                    'link' => '/quayso/atom',
                ],
                [
                    'icon' => '/asset/images/header/numbergame1.avif',
                    'label' => 'number game 1',
                    'link' => '/quayso/numbergame1',
                ],
                [
                    'icon' => '/asset/images/header/numbergame2.avif',
                    'label' => 'number game 2',
                    'link' => '/quayso/numbergame2',
                ],
                [
                    'icon' => '/asset/images/header/quayso1.avif',
                    'label' => __('pages.navigation.number_game_1'),
                    'link' => '/quayso/quayso1',
                ],
                [
                    'icon' => '/asset/images/header/quayso2.avif',
                    'label' => __('pages.navigation.number_game_2'),
                    'link' => '/quayso/quayso2',
                ],
                [
                    'icon' => '/asset/images/header/quayso5.avif',
                    'label' => __('pages.navigation.number_game_5'),
                    'link' => '/quayso/quayso5',
                ],
            ],
        ],
        [
            'href' => UrlPathEnum::GAME_CARD->value,
            'labelType' => null,
            'label' => __('pages.navigation.card_games'),
            'icon' => 'asset/images/header/icon/game-bai.svg',
        ],
        [
            'href' => UrlPathEnum::KENO->value,
            'labelType' => null,
            'label' => 'Keno',
            'icon' => 'asset/images/header/icon/keno.svg',
        ],
        [
            'href' => '',
            'labelType' => '',
            'label' => __('pages.navigation.lottery'),
            'activeList' => ['/lode-3-mien', '/lo-de-sieu-toc', '/mega645', '/power655'],
            'icon' => 'asset/images/header/icon/lode.svg',
            'list' => [
                [
                    'icon' => 'asset/images/header/lode3mien.avif',
                    'label' => __('pages.navigation.lottery_3_regions'),
                    'link' => '/lode-3-mien',
                ],
                [
                    'icon' => 'asset/images/header/lodesieutoc.avif',
                    'label' => __('pages.navigation.lottery_super_speed'),
                    'link' => '/lo-de-sieu-toc',
                ],
                [
                    'icon' => 'asset/images/header/mega645.avif',
                    'label' => 'Mega645',
                    'link' => '/mega645',
                ],
                [
                    'icon' => 'asset/images/header/power655.avif',
                    'label' => 'Power655',
                    'link' => '/power655',
                ],
                [
                    'icon' => '/asset/images/header/md5.avif',
                    'label' => __('pages.navigation.super_speed_md5'),
                    'link' => '/md5',
                ],
            ],
        ],
        [
            'href' => UrlPathEnum::FISHING->value,
            'labelType' => null,
            'label' => __('pages.navigation.fishing'),
            'icon' => 'asset/images/header/icon/banca.svg',
        ],
        [
            'labelType' => 'new',
            'label' => __('pages.navigation.cockfighting'),
            'checkAuth' => true,
            'activeList' => ['/daga/ga28', '/daga/ws168'],
            'icon' => 'asset/images/header/icon/daga.svg',
            'list' => [
                [
                    'icon' => 'asset/images/header/nav-cookfighting-a28.avif',
                    'label' => __('pages.navigation.cockfighting_ga28'),
                    'link' => '/daga/ga28',
                ],
                [
                    'icon' => 'asset/images/header/nav-cookfighting-ws168.avif',
                    'label' => __('pages.navigation.cockfighting_ws168'),
                    'link' => '/daga/ws168',
                ],
            ],
        ],
        [
            'href' => UrlPathEnum::GAME->value,
            'labelType' => null,
            'label' => __('pages.navigation.game_portal'),
            'icon' => 'asset/images/header/icon/cong-game.svg',
            'listLink' => [
                'cong-game/all',
                'cong-game/favorite',
                'cong-game/xo-so',
                'cong-game/table-games',
                'cong-game/quay-slots',
                'cong-game/game-nhanh',
                'cong-game/game-khac',
            ],
            'activeList' => [
                '/cong-game/all',
                '/cong-game/xo-so',
                '/cong-game/quay-slots',
                '/cong-game/table-games',
                '/cong-game/game-nhanh',
                '/cong-game/game-khac',
                '/cong-game/favorite',
                '/cong-game/quay-so-number-games',
            ],
        ],
    ],
    'navigationSection' => [
        'list' => [
            [
                'label' => 'Hot',
                'icon-name' => 'hot',
                'link' => UrlPathEnum::HOME->value,
            ],
            [
                'label' => __('pages.navigation.sports'),
                'icon-name' => 'sport',
                'link' => UrlPathEnum::SPORTS->value,
            ],
            [
                'label' => __('pages.navigation.casino'),
                'icon-name' => 'poker',
                'link' => UrlPathEnum::CASINO->value,
                'tag' => 'live',
            ],
            [
                'label' => __('pages.navigation.jackpot'),
                'icon-name' => 'lottery',
                'link' => UrlPathEnum::NOHU->value,
            ],
            [
                'label' => __('pages.navigation.number_games'),
                'icon-name' => 'slots',
                'link' => UrlPathEnum::QUAYSO->value,
                'type' => 'hot',
            ],
            [
                'label' => __('pages.navigation.card_games'),
                'icon-name' => 'gambit-game',
                'link' => UrlPathEnum::GAME_CARD->value,
            ],
            [
                'label' => __('pages.navigation.lottery'),
                'icon-name' => 'bingo',
                'link' => UrlPathEnum::LODE->value,
            ],
            [
                'label' => 'Keno',
                'icon-name' => 'keno',
                'link' => UrlPathEnum::KENO->value,
            ],
            [
                'label' => __('pages.navigation.fishing'),
                'icon-name' => 'fishing',
                'link' => UrlPathEnum::FISHING->value,
            ],
            [
                'label' => __('pages.navigation.cockfighting'),
                'icon-name' => 'fightcock',
                'link' => UrlPathEnum::FIGHTCOCK->value,
                'type' => 'new',
            ],
            [
                'label' => __('pages.navigation.game_portal'),
                'icon-name' => 'game-gate',
                'link' => UrlPathEnum::GAME->value,
            ],
            [
                'label' => __('pages.navigation.promotions'),
                'icon-name' => 'promotion',
                'link' => UrlPathEnum::EVENTS_PROMOTIONS->value,
            ],
            [
                'label' => __('pages.navigation.help'),
                'icon-name' => 'help',
                'link' => UrlPathEnum::HELP->value,
            ],
        ],
    ],
    'mbHiddenLinks' => [
        'en.information.index',
        'en.history.index',
        'en.notification.index',
        'en.about-us.index',
        'en.policy.index',
        'en.terms.index',
        'en.question.index',
        'en.disclaimer.index',
        'en.instruction.index',
        'en.instruction.deposit',
        'en.instruction.withdrawal',
        'en.instruction.p2p',
        'en.news.index',
        'en.news.detail',
        'en.news.category',
        'en.events.promo',
        'en.bank-account.index',
        'en.promotion.index',
    ],
];
