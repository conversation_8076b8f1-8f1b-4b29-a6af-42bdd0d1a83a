<?php

class ImprovedHardTextExtractor {
    private $viTexts = [];
    private $translations = [];
    
    // Vietnamese to English translation mapping
    private $translationMap = [
        // Common words
        'Tìm kiếm' => 'Search',
        'Tìm Kiếm' => 'Search',
        'Tìm kiếm ngân hàng' => 'Search bank',
        'Ngân hàng' => 'Bank',
        'Chọn ngân hàng của bạn' => 'Select your bank',
        'Chọn tài khoản ngân hàng của bạn' => 'Select your bank account',
        'Số tài khoản' => 'Account number',
        'Nhập số tài khoản' => 'Enter account number',
        'Tên tài khoản' => 'Account name',
        'Nhập tên tài khoản' => 'Enter account name',
        'Chủ tài khoản' => 'Account holder',
        'Số tiền rút' => 'Withdrawal amount',
        'Nhập số tiền rút' => 'Enter withdrawal amount',
        'Số điện thoại' => 'Phone number',
        'Nhập 5 số cuối điện thoại' => 'Enter last 5 digits of phone',
        '<PERSON>ác minh số điện thoại' => 'Verify phone number',
        'Địa chỉ ví' => 'Wallet address',
        'Nhập địa chỉ ví' => 'Enter wallet address',
        'Số lượng thẻ' => 'Card quantity',
        'Mã OTP' => 'OTP code',
        'Nhập mã OTP' => 'Enter OTP code',
        'Nhập Email của bạn' => 'Enter your email',
        'Xác minh Email' => 'Verify email',
        'Xác minh telegram' => 'Verify telegram',
        
        // Game types
        'Bắn cá' => 'Fishing',
        'Xóc đĩa' => 'Dice',
        'LÔ ĐỀ' => 'LOTTERY',
        'Sòng bài' => 'Casino',
        'Quay số' => 'Lucky draw',
        'Nổ hũ' => 'Jackpot',
        
        // Transaction types
        'Rút tiền' => 'Withdraw',
        'Nạp tiền' => 'Deposit',
        'Chuyển khoản' => 'Bank transfer',
        'Thẻ cào' => 'Phone card',
        'Hoàn trả Slots' => 'Slot cashback',
        'Thất bại' => 'Failed',
        'Đang xử lý' => 'Processing',
        'Hoàn thành' => 'Completed',
        'Khuyến mãi' => 'Promotion',
        
        // Status
        'CANCEL' => 'Failed',
        'DRAFT' => 'Processing',
        'FINISHED' => 'Completed',
        'PENDING' => 'Processing',
        'APPROVED' => 'Processing',
        'PROCESSING' => 'Processing',
        'WAITING' => 'Processing',
        'PHONE_CARD_PROCESSING' => 'Processing',
        'PHONE_CARD_PENDING' => 'Processing',
        'PHONE_CARD_FINISHED' => 'Completed',
        'PHONE_CARD_CANCEL' => 'Failed',
        'PHONE_CARD_DRAFT' => 'Processing',
        
        // Labels
        'Mới' => 'New',
        'Bảo trì' => 'Maintenance',
        'Đề xuất' => 'Recommended',
        'Gần đây' => 'Recent',
        'Nhà Cung Cấp' => 'Provider',
        'Mọi thắc mắc vui lòng liên hệ' => 'For any questions, please contact',
        'Live Chat' => 'Live Chat',
        'Telegram Support' => 'Telegram Support',
        'Khuyến Mãi Đang Sử Dụng' => 'Active Promotions',
        'Tài khoản ngân hàng của bạn' => 'Your bank account',
        'Vui lòng nhập tên đăng nhập và mật khẩu' => 'Please enter username and password',
        'Tên đăng nhập' => 'Username',
        'Mật khẩu' => 'Password',
        'Số điện thoại' => 'Phone number',
        'của' => 'of',
        'Từ chương trình' => 'From the program',
        'Nạp Giờ Vàng' => 'Golden Hour Deposit',
        'mỗi ngày' => 'every day',
        'Mỗi người chơi được hoàn trả' => 'Each player gets cashback',
        'trên phiếu cược thua, tối đa' => 'on losing bets, maximum',
        'Áp dụng cho sản phẩm cược thể thao' => 'Applies to sports betting products',
        'trên' => 'on',
        'Điều khoản và Điều kiện chung của' => 'General Terms and Conditions of',
        'vẫn được áp dụng cho chương trình này.' => 'still apply to this program.',
        'TỔNG CƯỢC CẦN ĐẠT' => 'TOTAL BETTING REQUIRED',
        'TỔNG CƯỢC CẦN HOÀN THÀNH' => 'TOTAL BETTING TO COMPLETE',
        'Hướng dẫn mua USDT' => 'USDT purchase guide',
        'Lưu ý' => 'Note',
        'Hướng Dẫn Nạp Tiền' => 'Deposit Guide',
        'CẢNH BÁO' => 'WARNING',
        'Xác nhận' => 'Confirm',
        'Từ chối' => 'Reject',
        'Nhận' => 'Receive',
        'Số serial' => 'Serial number',
        'Nhập số serial' => 'Enter serial number',
        'Mã thẻ (PIN)' => 'Card code (PIN)',
        'Nhập mã thẻ (PIN)' => 'Enter card code (PIN)',
        'Nhập số tiền nạp' => 'Enter deposit amount',
        'Số tiền nạp' => 'Deposit amount',
        'Khuyến mãi đang sử dụng' => 'Active promotions',
        'Chọn tài khoản' => 'Select account',
        'Hướng dẫn giao dịch P2P' => 'P2P transaction guide',
        'Hướng dẫn đăng ký' => 'Registration guide',
        'Hướng dẫn nạp tiền' => 'Deposit guide',
        'Hướng dẫn rút tiền' => 'Withdrawal guide',
        'Giới thiệu nhà cái' => 'Casino introduction',
        'Điều khoản và điều kiện' => 'Terms and conditions',
        'Chính sách bảo mật' => 'Privacy policy',
        'Lô đề' => 'Lottery',
        'Miễn trách nhiệm' => 'Disclaimer',
        'Câu hỏi thường gặp' => 'Frequently asked questions',
        'Trò chơi nổi bật' => 'Featured games',
        'Xem thêm' => 'See more',
        'Đề xuất' => 'Recommended',
    ];
    
    public function extractAndReplace() {
        echo "Bắt đầu tìm kiếm và thay thế hard text...\n";
        
        // Tìm tất cả file PHP và Blade template
        $files = $this->findFiles();
        
        // Tìm tất cả hard text trước
        foreach ($files as $file) {
            $this->extractFromFile($file);
        }
        
        // Tạo file ngôn ngữ
        $this->createLanguageFiles();
        
        // Thay thế trong tất cả file
        foreach ($files as $file) {
            $this->replaceInFile($file);
        }
        
        echo "Hoàn thành! Đã tìm thấy " . count($this->viTexts) . " hard text.\n";
    }
    
    private function findFiles() {
        $files = [];
        
        // Tìm file PHP
        $phpFiles = glob('**/*.php', GLOB_BRACE);
        foreach ($phpFiles as $file) {
            if (strpos($file, 'lang/') === false && 
                strpos($file, 'vendor/') === false && 
                strpos($file, 'extract_hard_text.php') === false &&
                strpos($file, 'improved_extract_hard_text.php') === false) {
                $files[] = $file;
            }
        }
        
        // Tìm file Blade template
        $bladeFiles = glob('**/*.blade.php', GLOB_BRACE);
        foreach ($bladeFiles as $file) {
            if (strpos($file, 'lang/') === false && 
                strpos($file, 'vendor/') === false) {
                $files[] = $file;
            }
        }
        
        return $files;
    }
    
    private function extractFromFile($file) {
        if (!file_exists($file)) {
            return;
        }
        
        $content = file_get_contents($file);
        
        // Tìm các chuỗi tiếng Việt trong chuỗi
        preg_match_all('/[\'"]([^\'"]*[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ][^\'"]*)[\'"]/', $content, $matches);
        
        if (!empty($matches[1])) {
            foreach ($matches[1] as $text) {
                $text = trim($text);
                if (strlen($text) > 1 && !preg_match('/^[0-9\s\-\+\(\)\.\,\:\;\?\!]+$/', $text)) {
                    $this->viTexts[$text] = $text;
                }
            }
        }
        
        // Tìm các chuỗi trong Blade template
        preg_match_all('/\{\{\s*[\'"]([^\'"]*[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ][^\'"]*)[\'"]\s*\}\}/', $content, $matches);
        
        if (!empty($matches[1])) {
            foreach ($matches[1] as $text) {
                $text = trim($text);
                if (strlen($text) > 1 && !preg_match('/^[0-9\s\-\+\(\)\.\,\:\;\?\!]+$/', $text)) {
                    $this->viTexts[$text] = $text;
                }
            }
        }
        
        // Tìm các chuỗi trong placeholder và label attributes
        preg_match_all('/placeholder=[\'"]([^\'"]*[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ][^\'"]*)[\'"]/', $content, $matches);
        
        if (!empty($matches[1])) {
            foreach ($matches[1] as $text) {
                $text = trim($text);
                if (strlen($text) > 1 && !preg_match('/^[0-9\s\-\+\(\)\.\,\:\;\?\!]+$/', $text)) {
                    $this->viTexts[$text] = $text;
                }
            }
        }
        
        preg_match_all('/label=[\'"]([^\'"]*[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ][^\'"]*)[\'"]/', $content, $matches);
        
        if (!empty($matches[1])) {
            foreach ($matches[1] as $text) {
                $text = trim($text);
                if (strlen($text) > 1 && !preg_match('/^[0-9\s\-\+\(\)\.\,\:\;\?\!]+$/', $text)) {
                    $this->viTexts[$text] = $text;
                }
            }
        }
    }
    
    private function createLanguageFiles() {
        // Tạo file tiếng Việt
        $viContent = "<?php\n\nreturn [\n";
        $enContent = "<?php\n\nreturn [\n";
        
        foreach ($this->viTexts as $viText) {
            $key = $this->generateKey($viText);
            $enText = $this->translationMap[$viText] ?? $viText; // Fallback to original if no translation
            
            $viContent .= "    '{$key}' => '{$viText}',\n";
            $enContent .= "    '{$key}' => '{$enText}',\n";
        }
        
        $viContent .= "];\n";
        $enContent .= "];\n";
        
        // Tạo file mới
        file_put_contents('lang/vi/hard_text.php', $viContent);
        file_put_contents('lang/en/hard_text.php', $enContent);
        
        echo "Đã tạo file lang/vi/hard_text.php và lang/en/hard_text.php\n";
    }
    
    private function generateKey($text) {
        // Loại bỏ dấu tiếng Việt và chuyển thành key
        $key = strtolower($text);
        $key = preg_replace('/[àáạảãâầấậẩẫăằắặẳẵ]/', 'a', $key);
        $key = preg_replace('/[èéẹẻẽêềếệểễ]/', 'e', $key);
        $key = preg_replace('/[ìíịỉĩ]/', 'i', $key);
        $key = preg_replace('/[òóọỏõôồốộổỗơờớợởỡ]/', 'o', $key);
        $key = preg_replace('/[ùúụủũưừứựửữ]/', 'u', $key);
        $key = preg_replace('/[ỳýỵỷỹ]/', 'y', $key);
        $key = preg_replace('/[đ]/', 'd', $key);
        $key = preg_replace('/[^a-z0-9\s]/', '', $key);
        $key = preg_replace('/\s+/', '_', $key);
        $key = trim($key, '_');
        
        return $key;
    }
    
    private function replaceInFile($file) {
        if (!file_exists($file)) {
            return;
        }
        
        $content = file_get_contents($file);
        $originalContent = $content;
        
        foreach ($this->viTexts as $viText) {
            $key = $this->generateKey($viText);
            
            // Thay thế trong chuỗi PHP
            $content = str_replace("'{$viText}'", "__('hard_text.{$key}')", $content);
            $content = str_replace("\"{$viText}\"", "__('hard_text.{$key}')", $content);
            
            // Thay thế trong Blade template
            $content = str_replace("{{ '{$viText}' }}", "{{ __('hard_text.{$key}') }}", $content);
            $content = str_replace("{{ \"{$viText}\" }}", "{{ __('hard_text.{$key}') }}", $content);
            
            // Thay thế trong placeholder và label attributes
            $content = str_replace("placeholder=\"{$viText}\"", "placeholder=\"{{ __('hard_text.{$key}') }}\"", $content);
            $content = str_replace("placeholder='{$viText}'", "placeholder=\"{{ __('hard_text.{$key}') }}\"", $content);
            $content = str_replace("label=\"{$viText}\"", "label=\"{{ __('hard_text.{$key}') }}\"", $content);
            $content = str_replace("label='{$viText}'", "label=\"{{ __('hard_text.{$key}') }}\"", $content);
        }
        
        if ($content !== $originalContent) {
            // Tạo backup
            copy($file, $file . '.backup');
            
            // Ghi file mới
            file_put_contents($file, $content);
            
            echo "Đã cập nhật file: {$file}\n";
        }
    }
}

// Chạy script
// $extractor = new ImprovedHardTextExtractor();
// $extractor->extractAndReplace();

echo "\nHoàn thành!\n"; 