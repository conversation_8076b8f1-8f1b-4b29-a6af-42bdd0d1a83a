<?php

return [
    'title' => 'Instructions',
    'help' => 'Help',
    'instructionItems' => [
        'p2p' => 'P2P TRANSACTION GUIDE',
        'register' => 'Registration guide',
        'deposit' => 'Deposit guide',
        'withdrawal' => 'Withdrawal guide',
    ],
    'mbInstructionItems' => [
        'p2p' => 'P2P',
        'register' => 'registration',
        'deposit' => 'Deposit',
        'withdrawal' => 'WITHDRAWAL',
    ],
    'content' => [
        'title' => ':brandName - Leading online casino',
        'description' => 'Registering to become a member of :brandName is extremely simple with the following steps:',
        'step1' => 'On the :brandName homepage => Click the REGISTER button in the top right corner of the screen.',
        'step2' => 'Fill in all the information in the corresponding fields',
        'step1Title' => 'Step 1:',
        'step2Title' => 'Step 2:',
        'stepItems' => [
            'item1' => 'Username: Minimum 6 characters, no spaces.',
            'item2' => 'Phone number: Enter your current phone number.',
            'item3' => 'Password: Minimum 6 characters, no spaces.',
            'item4' => 'Finally click the REGISTER button.',
        ],
    ],
    'deposit' => [
        'title' => 'Instructions',
        'codepay' => [
            'title' => '1. Codepay Deposit',
            'step1' => [
                'title' => 'Step 1:',
                'description' => 'Login to :brandName and select Deposit → Codepay',
                'img' => '/asset/images/instruction/deposit/img-instruction-codepay-deposit.avif',
                'img_mb' => '/asset/images/instruction/deposit/mb/img-instruction-codepay-deposit.avif',
            ],
            'step2' => [
                'title' => 'Step 2:',
                'description' => 'Enter the amount you want to transfer and generate QR code',
            ],
            'step3' => [
                'title' => 'Step 3:',
                'description' => 'Login to your bank\'s internet banking account to transfer money, or scan the QR code on the corresponding bank page for faster transfers.',
            ],
            'step4' => [
                'title' => 'Step 4:',
                'description' => 'Fill in the transfer content with the code provided by the system.',
            ],
            'note' => [
                'title' => 'Note:',
                'items' => [
                    'item1' => 'Please fill in the content: Enter the content provided when making bank transfers to update successful transfers',
                    'item2' => 'Ultra-fast deposit in 2 minutes, no need to create tickets',
                    'item3' => 'Each User has 1 unique transfer code, this code can be used multiple times',
                    'item4' => 'YOU CAN TRANSFER TO ANY :brandName ACCOUNT VIA INTERBANK TRANSFER',
                ],
            ],
        ],
        'crypto' => [
            'title' => '2. Crypto deposit',
            'step1' => [
                'title' => 'Step 1:',
                'description' => 'Login to :brandName and select Deposit → Crypto.',
                'img' => '/asset/images/instruction/deposit/img-instruction-crypto-deposit.avif',
                'img_mb' => '/asset/images/instruction/deposit/mb/img-instruction-crypto-deposit.avif',
            ],
            'step2' => [
                'title' => 'Step 2:',
                'description' => 'Go to the crypto deposit page to get the unique wallet address for each customer.',
            ],
            'step3' => [
                'title' => 'Step 3:',
                'description' => 'Transfer crypto to the wallet address in step 1 (USDT - TRC20).',
            ],
            'step4' => [
                'title' => 'Step 4:',
                'description' => 'Wait 2-5 minutes for the system to automatically confirm the deposit.',
            ],
            'note' => [
                'title' => 'Note: You can scan the QR code for faster transactions.',
                'items' => [],
            ],
        ],
        'ewallet' => [
            'title' => '3. Deposit via e-wallet',
            'step1' => [
                'title' => 'Step 1:',
                'description' => 'Login to :brandName and select Deposit → E-wallet.',
                'img' => '/asset/images/instruction/deposit/img-instruction-ewallet-deposit.avif',
                'img_mb' => '/asset/images/instruction/deposit/mb/img-instruction-ewallet-deposit.avif',
            ],
            'step2' => [
                'title' => 'Step 2:',
                'description' => 'Get information and transfer money.',
                'items' => [
                    'item1' => 'Get the phone number and account holder information of :brandName. Then login to your e-wallet account and proceed to transfer money to :brandName\'s account',
                    'item2' => 'Please fill in the transfer message as the "Transfer content" provided by the system',
                    'item3' => 'Confirm the deposit in your e-wallet and wait 30 seconds for the money to be automatically transferred to your betting account',
                ],
            ],
            'note' => [
                'title' => 'Note:',
                'items' => [
                    'item1' => '100% promotions do not apply when depositing via e-wallet',
                    'item2' => 'After transferring, wait about 30 SECONDS for the money to be deposited into your account',
                    'item3' => 'If the transfer does not fill in or fills in the wrong content, you will not receive the money immediately',
                    'item4' => 'After 3 minutes if you haven\'t received the money, please contact SUPPORT 24/24',
                ],
            ],
        ],
        'phone_card' => [
            'title' => '4. Phone card deposit',
            'step1' => [
                'title' => 'Step 1:',
                'description' => 'Login to :brandName and select Deposit → Phone card.',
            ],
            'step2' => [
                'title' => 'Step 2:',
                'description' => 'Fill in all phone card information.',
                'items' => [
                    'item1' => 'Network: Select the network you want to deposit',
                    'item2' => 'Denomination: Click to select the phone card denomination (please select the correct denomination on the card)',
                    'item3' => 'Card PIN: Enter the correct PIN code of the phone card',
                    'item4' => 'Card serial number: Enter the correct serial number on the corresponding card',
                ],
            ],
            'step3' => [
                'title' => 'Step 3:',
                'description' => 'Click "DEPOSIT".',
                'img' => '/asset/images/instruction/deposit/img-instruction-phone-card-deposit.avif',
                'img_mb' => '/asset/images/instruction/deposit/mb/img-instruction-phone-card-deposit.avif',
            ],
            'note' => [
                'title' => 'Note:',
                'items' => [
                    'item1' => 'Please select the correct denomination and network. If you select incorrectly we will not be responsible',
                    'item2' => 'Lowest card processing fees in the market (Viettel 12%; Vinaphone 11%; Mobifone 13%; Vietnamobile 11%)',
                    'item3' => '100% promotions do not apply when depositing via phone cards',
                ],
            ],
        ],
    ],
    'withdrawal' => [
        'title' => 'Instructions',
        'description' => [
            'text' => 'Withdrawal is the process of taking money after the player has won. We support 4 withdrawal methods including:',
            'items' => [
                'item1' => 'Withdraw via bank account.',
                'item2' => 'Withdraw via phone card.',
                'item3' => 'Withdraw via crypto.',
                // 'item4' => 'Withdraw via coin 12.',
            ],
        ],
        'bank' => [
            'title' => '1. Bank withdrawal guide',
            'step1' => [
                'title' => 'Step 1:',
                'description' => 'Login to :brandName → select withdraw → withdraw via bank',
                // 'img' => '/asset/images/instruction/withdrawal/img-instruction-bank.avif',
                // 'img_mb' => '/asset/images/instruction/withdrawal/mb/img-instruction-bank.avif',

                // 'img_2' => '/asset/images/instruction/withdrawal/img-instruction-bank-2.avif',
                // 'img_mb_2' => '/asset/images/instruction/withdrawal/mb/img-instruction-bank-2.avif',
                // 'img_2' => '/asset/images/brand/instruction-bank-2.avif',
                // 'img_mb_2' => '/asset/images/brand/mb/instruction-bank-2.avif',
            ],
            'step2' => [
                'title' => 'Step 2:',
                'description' => 'Please fill in all the information as follows:',
                'items' => [
                    'item1' => 'Select bank: Choose the bank you want to withdraw to.',
                    'item2' => 'Account number: Enter the account number to receive money.',
                    'item3' => 'Account holder: Enter the name of the account holder.',
                    'item4' => 'Withdrawal amount: Enter the amount you want to withdraw, 1K = 1000VND (Example: 1,000,000 VND enter 1,000).',
                ],
            ],
            'step3' => [
                'title' => 'Step 3:',
                'description' => 'Click the WITHDRAW button and wait a few minutes for the system to check and transfer money to your account.',
            ],
            'note' => [
                'title' => 'Note: We are not responsible for customers entering incorrect receiving information or accounts being HACKED.',
                'items' => [],
                'img' => '/asset/images/instruction/withdrawal/img-instruction-bank.avif',
                'img_mb' => '/asset/images/instruction/withdrawal/mb/img-instruction-bank.avif',
            ],
            'step4' => [
                'title' => '',
                'description' => 'For cases where you don\'t have your own bank account, you can directly enter your bank to withdraw money, the system will automatically save the bank in the "Bank Account" section.',
                'img' => '/asset/images/instruction/withdrawal/img-instruction-bank-default.avif',
                'img_mb' => '/asset/images/instruction/withdrawal/mb/img-instruction-bank-default.avif',
            ],
        ],
        'phone_card' => [
            'title' => '2. Phone card withdrawal guide',
            'step1' => [
                'title' => 'Step 1:',
                'description' => 'Login to :brandName → click the icon in the top right corner of the screen and select withdraw → withdraw via phone card',
            ],
            'step2' => [
                'title' => 'Step 2:',
                'description' => 'Please fill in all the information as follows:',
                'items' => [
                    'item1' => 'Select network: Choose the network you want to withdraw cards from.',
                    'item2' => 'Select amount: Choose the card denomination.',
                    'item3' => 'Number of phone cards: Choose the number of cards corresponding to the selected denomination.',
                ],
                'img' => '/asset/images/instruction/withdrawal/img-instruction-phone-card.avif',
                'img_mb' => '/asset/images/instruction/withdrawal/mb/img-instruction-phone-card.avif',
            ],
            'step3' => [
                'title' => 'Step 3:',
                'description' => 'Click withdraw and wait for the system to process',
                'sub_description' => 'After clicking withdraw, go to the transaction history section to get the phone card information',
            ],
            'note' => [
                'title' => 'Note: Transaction history only shows for 12 hours, after withdrawing phone cards please get the card information immediately in the transaction history section.',
                'items' => [],
            ],
        ],
        'crypto' => [
            'title' => '3. Crypto withdrawal guide',
            'step1' => [
                'title' => 'Step 1:',
                'description' => 'Login to :brandName → click the icon in the top right corner of the screen and select withdraw → withdraw crypto',
            ],
            'step2' => [
                'title' => 'Step 2:',
                'description' => 'Fill in all crypto withdrawal information.',
                'items' => [
                    'item1' => 'Select crypto type: Choose USDT (TRC20).',
                    'item2' => 'Receiving wallet address: Enter your USDT wallet address on TRC20 network.',
                    'item3' => 'Confirm phone number (last 5 digits): Enter the last 5 digits of your registered phone number.',
                    'item4' => 'Enter amount: Enter the amount you need to withdraw.',
                ],
                'img' => '/asset/images/instruction/withdrawal/img-instruction-crypto.avif',
                'img_mb' => '/asset/images/instruction/withdrawal/mb/img-instruction-crypto.avif',
            ],
            'step3' => [
                'title' => 'Step 3:',
                'description' => 'Click withdraw and wait for the system to process',
                'sub_description' => 'After clicking withdraw, go to the transaction history section to check again',
            ],
            'note' => [
                'title' => 'Note:',
                'items' => [
                    'item1' => 'Please enter the correct USDT wallet address, we are not responsible if customers enter incorrect USDT wallet information.',
                    'item2' => 'Transaction history only shows for 12 hours',
                ],
            ],
        ],
        // 'coin12' => [
        //     'title' => '4. Coin12 withdrawal guide',
        //     'step1' => [
        //         'title' => 'Step 1:',
        //         'description' => 'Login to :brandName → click the icon in the top right corner of the screen and select withdraw → withdraw coin12',
        //     ],
        //     'step2' => [
        //         'title' => 'Step 2:',
        //         'description' => 'Fill in all coin12 withdrawal information.',
        //         'items' => [
        //             'item1' => 'Select crypto type: Choose USDT (TRC20).',
        //             'item2' => 'Receiving wallet address: Enter your USDT wallet address on TRC20 network.',
        //             'item3' => 'Confirm phone number (last 5 digits): Enter the last 5 digits of your registered phone number.',
        //             'item4' => 'Enter amount: Enter the amount you need to withdraw.',
        //         ],
        //         'img' => '/asset/images/instruction/withdrawal/img-instruction-coin12.avif',
        //         'img_mb' => '/asset/images/instruction/withdrawal/mb/img-instruction-coin12.avif',
        //     ],
        //     'step3' => [
        //         'title' => 'Step 3:',
        //         'description' => 'Click withdraw and wait for the system to process',
        //         'sub_description' => 'After clicking withdraw, go to the transaction history section to check again',
        //     ],
        //     'note' => [
        //         'title' => 'Note:',
        //         'items' => [
        //             'item1' => 'Please enter the correct USDT wallet address, we are not responsible if customers enter incorrect USDT wallet information.',
        //             'item2' => 'Transaction history only shows for 12 hours',
        //         ]
        //     ]
        // ]
    ],
    'p2p' => [
        'title' => 'Instructions',
        'exchange' => [
            'title' => '1. Kcoin conversion guide',
            'step1' => [
                'title' => 'Step 1:',
                'description' => 'Login to :brandName',
            ],
            'step2' => [
                'title' => 'Step 2:',
                'description' => 'On the Homepage, Click on [Account] or [Deposit] → [P2P] on the secondary toolbar of the screen',
            ],
            'step3' => [
                'title' => 'Step 3:',
                'description' => 'In the top right corner of the screen will display the number of Kcoins you have. Click this button to convert between the balance in your :brandName account and Kcoin account.',
                'img' => '/asset/images/instruction/p2p/img-instruction-exchange-1.avif',
                'img_mb' => '/asset/images/instruction/p2p/mb/img-instruction-exchange-1.avif',
            ],
            'step4' => [
                'title' => 'Step 4:',
                'description' => 'Enter the amount of K you want to convert to Kcoin to trade with merchants on the exchange and Click the [Convert] button.',
            ],
            'note' => [
                'title' => 'Note: To convert from K to Kcoin and vice versa, you just need to click on the gray arrow pointing down (arrow position between "Main Account" and "Kcoin Account").',
                'items' => [],
            ],
            'step5' => [
                'title' => 'Step 5:',
                'description' => 'Click [Confirm] to complete the conversion and wait for customer service to approve the conversion ticket.',
                'img' => '/asset/images/instruction/p2p/img-instruction-exchange-2.avif',
                'img_mb' => '/asset/images/instruction/p2p/mb/img-instruction-exchange-2.avif',
            ],
        ],
        'sell' => [
            'title' => '2. Kcoin Selling Guide',
            'step1' => [
                'title' => 'Step 1:',
                'description' => 'Login to :brandName',
            ],
            'step2' => [
                'title' => 'Step 2:',
                'description' => 'On the Homepage, Click on [Account] or [Deposit] → [P2P] on the secondary toolbar of the screen',
            ],
            'step3' => [
                'title' => 'Step 3:',
                'description' => 'Choose merchant, select [SELL]',
            ],
            'note' => [
                'title' => 'Note: You need to ensure your wallet has Kcoin balance to perform the sell transaction.',
                'items' => [],
                'img' => '/asset/images/instruction/p2p/img-instruction-sell-1.avif',
                'img_mb' => '/asset/images/instruction/p2p/mb/img-instruction-sell-1.avif',
            ],
            'step4' => [
                'title' => 'Step 4:',
                'description' => 'The screen will then display a box as shown below, Enter the amount you want to sell in the [Enter sell amount] box',
                'sub_description' => 'In [Select payment method], the system will automatically add the bank account number that you have added to your account.',
            ],
            'note2' => [
                'title' => 'Note: Please check once more to ensure the correct bank account number before clicking [Sell]',
                'items' => [],
                'img' => '/asset/images/instruction/p2p/img-instruction-sell-2.avif',
                'img_mb' => '/asset/images/instruction/p2p/mb/img-instruction-sell-2.avif',
            ],
            'step5' => [
                'title' => 'Step 5:',
                'description' => 'The screen will switch to the [Transaction History] tab which contains detailed information about the order you just sold and displays a countdown timer.',
                'img' => '/asset/images/instruction/p2p/img-instruction-sell-3.avif',
                'img_mb' => '/asset/images/instruction/p2p/mb/img-instruction-sell-3.avif',
            ],
            'step6' => [
                'title' => 'Step 6:',
                'description' => 'At this point the merchant will proceed to transfer money to your account and complete the transaction. Here you don\'t need to do anything else.',
                'sub_description' => 'If you still haven\'t received the money, please contact customer service for support.',
                'img' => '/asset/images/instruction/p2p/img-instruction-sell-4.avif',
                'img_mb' => '/asset/images/instruction/p2p/mb/img-instruction-sell-4.avif',
            ],
        ],
        'buy' => [
            'title' => '3. Kcoin Buying Guide',
            'step1' => [
                'title' => 'Step 1:',
                'description' => 'Login to :brandName',
            ],
            'step2' => [
                'title' => 'Step 2:',
                'description' => 'On the Homepage, Click on [Account] or [Deposit] → [P2P] on the secondary toolbar of the screen',
                'img' => '/asset/images/instruction/p2p/img-instruction-buy-1.avif',
                'img_mb' => '/asset/images/instruction/p2p/mb/img-instruction-buy-1.avif',
            ],
            'step3' => [
                'title' => 'Step 3:',
                'description' => 'Choose merchant, select [BUY]',
            ],
            'note' => [
                'title' => 'Note: "Limit" is the minimum and maximum amount that the merchant can pay you in 1 transaction. Pay attention to the "Available Quantity" and "Limit" of each advertisement to choose a merchant suitable for the amount of Kcoin you want to buy.',
                'items' => [],
                'img' => '/asset/images/instruction/p2p/img-instruction-buy-2.avif',
                'img_mb' => '/asset/images/instruction/p2p/mb/img-instruction-buy-2.avif',
            ],
            'step4' => [
                'title' => 'Step 4:',
                'description' => 'The screen will switch to [Transaction History] which contains detailed information about the order you just bought. You can choose the bank account that the merchant supports for payment in the [Select Bank] column and proceed to scan the QR code displayed on the screen to pay the seller.',
                'img' => '/asset/images/instruction/p2p/img-instruction-buy-3.avif',
                'img_mb' => '/asset/images/instruction/p2p/mb/img-instruction-buy-3.avif',
            ],
            'step5' => [
                'title' => 'Step 5:',
                'description' => 'At this point you have completed your transaction and wait for the buyer to transfer Kcoin to your wallet.',
                'sub_description' => 'Note: When the seller receives the money into their account, they will immediately transfer Kcoin to your wallet, you just need to wait and don\'t need to do anything else. The screen will display as (figure 11) when the buyer has received the money and completed the transaction.',
                'img' => '/asset/images/instruction/p2p/img-instruction-buy-4.avif',
                'img_mb' => '/asset/images/instruction/p2p/mb/img-instruction-buy-4.avif',
            ],
        ],
    ],
];
