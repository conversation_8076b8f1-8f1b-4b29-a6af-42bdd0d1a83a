/**
 * JavaScript Translation Helper
 * Provides translation functionality for JavaScript files
 */

class Translation {
    constructor() {
        this.translations = {};
        this.locale = document.documentElement.lang || 'vi';
        this.fallbackLocale = 'vi';
        this.loaded = false;
    }

    /**
     * Load translations for current locale
     */
    async load() {
        if (this.loaded) return;

        try {
            const response = await fetch(`/js/translations/${this.locale}`);
            if (response.ok) {
                this.translations = await response.json();
                this.loaded = true;
            } else {
                console.warn(`Failed to load translations for locale: ${this.locale}`);
                // Try fallback locale
                if (this.locale !== this.fallbackLocale) {
                    const fallbackResponse = await fetch(`/js/translations/${this.fallbackLocale}`);
                    if (fallbackResponse.ok) {
                        this.translations = await fallbackResponse.json();
                        this.loaded = true;
                    }
                }
            }
        } catch (error) {
            console.error('Error loading translations:', error);
        }
    }

    /**
     * Get translation by key
     * @param {string} key - Translation key (e.g., 'auth.username_required')
     * @param {object} replacements - Object with replacement values
     * @returns {string} Translated text
     */
    get(key, replacements = {}) {
        if (!this.loaded) {
            console.warn('Translations not loaded yet. Call await trans.load() first.');
            return key;
        }

        const keys = key.split('.');
        let value = this.translations;

        // Navigate through nested object
        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                console.warn(`Translation key not found: ${key}`);
                return key;
            }
        }

        if (typeof value !== 'string') {
            console.warn(`Translation value is not a string: ${key}`);
            return key;
        }

        // Replace placeholders
        let result = value;
        for (const [placeholder, replacement] of Object.entries(replacements)) {
            const regex = new RegExp(`:${placeholder}`, 'g');
            result = result.replace(regex, replacement);
        }

        return result;
    }

    /**
     * Check if translation exists
     * @param {string} key - Translation key
     * @returns {boolean}
     */
    has(key) {
        if (!this.loaded) return false;

        const keys = key.split('.');
        let value = this.translations;

        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return false;
            }
        }

        return typeof value === 'string';
    }

    /**
     * Set locale and reload translations
     * @param {string} locale - New locale
     */
    async setLocale(locale) {
        if (this.locale !== locale) {
            this.locale = locale;
            this.loaded = false;
            await this.load();
        }
    }

    /**
     * Get current locale
     * @returns {string}
     */
    getLocale() {
        return this.locale;
    }
}

// Create global instance
window.trans = new Translation();

// Auto-load translations when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.trans.load();
    });
} else {
    window.trans.load();
}

// Helper function for easier usage
window.__ = function(key, replacements = {}) {
    return window.trans.get(key, replacements);
};

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Translation;
}
