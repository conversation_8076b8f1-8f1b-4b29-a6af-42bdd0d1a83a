@use "variables";
@use "mixins";
@tailwind base;
@tailwind components;
@tailwind utilities;
@import "./pages/home/<USER>";
@import "./pages/home/<USER>";
@import "./pages/home/<USER>";
@import "./pages/home/<USER>";
@import "./pages/news/index.scss";
@import "./components/modal/style.scss";
@import "./components/modal/auth-modal.scss";
@import "./components/custom-toast.scss";
@import "./components/dropdown/providers-dropdown.scss";
@import "./pages/sports/index.scss";
@import "./pages/account/information.scss";
@import "./pages/account/styles.scss";
@import "./pages/account/information.scss";
@import "./pages/account/overview.scss";
@import "./pages/account/promotion.scss";
@import "./pages/deposit/styles.scss";
@import "./pages/instruction/styles.scss";
@import "toastify-js/src/toastify.css";
@import "./components/swiper.scss";
@import "./components/input/base.scss";
@import "./components/img.scss";
@import "./components/bottom-menu/styles.scss";
@import "./components/game-filter/styles.scss";

@font-face {
    font-family: "SVN_VT_Redzone";
    src: url("../fonts/SVN_VT_Redzone_Classic.otf") format("opentype");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "UTM Facebook";
    src: url("../fonts/00091-UTM-Facebook.ttf") format("opentype");
    font-weight: normal;
    font-style: normal;
}

@font-face {
  font-family: 'SVN-Helvetica Neue Heavy';
  src: url('../fonts/SVN-HelveticaNeue-Heavy.ttf') format('opentype');
  font-weight: normal;
  font-style: normal;
}

@layer utilities {
    /* Hide scrollbar for Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }
    /* Hide scrollbar for IE, Edge and Firefox */
    .no-scrollbar {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }
}

@layer utilities {
    .secondary-filter {
        filter: brightness(0) saturate(100%) invert(59%) sepia(100%) saturate(219%) hue-rotate(0deg) brightness(88%) contrast(90%);
    }

    .orange-gradient-filter {
        filter: brightness(0) saturate(100%) invert(56%) sepia(75%) saturate(5497%) hue-rotate(360deg) brightness(103%) contrast(104%);
    }
}

.loader-image-transparent {
    background: transparent url("/resources/img/spinner.svg") center center
        no-repeat;
}

.game-type-active {
    filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%)
        hue-rotate(338deg) brightness(102%) contrast(103%);
}

.no-scrollbar::-webkit-scrollbar {
    display: none;
}

.uppercase-first-letter {
    &::first-letter {
        text-transform: uppercase;
    }
}

.no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.live-chat-header {
    writing-mode: vertical-lr;
}

.filter-active {
    @apply xl:bg-primary-500  xl:text-neutral text-primary-500;
    i {
        @apply text-primary-500 xl:text-neutral;
    }
    .dropdown-label {
        @apply text-primary-600;
    }
}
.provider-active {
    @apply text-primary-500;

    i {
        @apply text-primary-500;
    }
    .dropdown-label {
        @apply text-primary-600;
    }
}

.border-active {
    /* @apply border-[#b01e46] text-black font-semibold; */
}
.border-inactive {
    /* @apply border-grey-200 text-grey; */
}

.btn-amount {
    /* @apply cursor-pointer rounded-md border border-grey-300 bg-white p-2 text-center text-sm font-medium text-grey-900 hover:border-y-yellow-400 hover:bg-yellow-400; */
}

.autoplay-swiper {
    -webkit-transition-timing-function: linear !important;
    transition-timing-function: linear !important;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
input[type="number"] {
    -moz-appearance: textfield;
}

.scrollbar {
    &::-webkit-scrollbar {
        @apply w-[4px];
    }

    &::-webkit-scrollbar-track {
        @apply bg-transparent;
    }

    &::-webkit-scrollbar-thumb {
        @apply bg-scrollbar;
    }

    &::-webkit-scrollbar-thumb:hover {
        @apply bg-scrollbar;
    }
}

/* Hide scrollbar for Chrome, Safari and Opera */
.hide-scroll::-webkit-scrollbar {
    @apply hidden;
}

/* Hide scrollbar for IE, Edge and Firefox */
.hide-scroll {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
}

.icon-eye {
    @apply translate-y-[1px];
}

input {
    &:focus-visible {
        @apply outline-none;
    }
}

@keyframes ping {
    0% {
        transform: scale(1);
    }
    12.5% {
        transform: scale(1.2);
    }

    25% {
        transform: scale(1);
    }
    27.5% {
        transform: scale(1);
    }
    50% {
        transform: scale(1);
    }
    62.5% {
        transform: scale(1);
    }
    75% {
        transform: scale(1);
    }
    87.5% {
        transform: scale(1);
    }
    100% {
        transform: scale(1);
    }
}

.mini-game {
    @apply cursor-pointer w-[70px] xl:w-[90px] z-[12] bg-no-repeat inset-[400px_0px_auto_auto] fixed right-[20px] top-[50vh] xl:top-[82vh] xl:right-[70px];

    .ping {
        animation: ping 5s cubic-bezier(.15, .55, .15, .50) infinite;

        &.game-text {
            animation-delay: 800ms;
        }
    }
    img {
        &:not(.btn-close) {
            @apply pointer-events-none;
        }
    }
}
.mini-game-ct {
    @apply z-[53];
    
    div[style*="background: transparent"] {
        @apply cursor-pointer xl:cursor-auto;
    }
}

.promotion-swiper,.card-group-lottery {
    @apply max-xl:pr-[10px];
}

@keyframes jackpot {
    0% {
        transform: scale(1);
    }

    to {
        transform: scale(1.1);
    }
}
#content-dropdown {
    &::-webkit-scrollbar {
        @apply w-[4px];
    }

    &::-webkit-scrollbar-thumb {
        @apply bg-neutral-200 rounded-[80px];
    }

    &::-webkit-scrollbar-track {
        @apply bg-neutral;
    }
}

.text-warning-500 {
    color: #CA8A04;
}

.text-success-600 {
    color: #16A34A;
}

.bg-warning-500 {
    background-color: #CA8A04;
}

.bg-success-600 {
    background-color: #16a34a;
}

#auth-modal {
    @apply absolute top-0 left-0 z-[54];
}

#noti-modal {
    @apply absolute top-0 left-0 z-[55];
}

.bottom-bar-background {
    filter: drop-shadow(0 1.5px 2.5px #4c5057);
}

body {
    &:has(.logout-modal-container) {
        overflow: hidden;
    }
    .container {
        @apply px-2.5;
    }
}

.notification-item-readed {
    @apply bg-neutral-50;
}

.codepay-deposit-status {
    @apply text-xs text-black-400;

    &.pending {
        @apply text-black-400;
    }

    &.expired {
        @apply text-danger-600 font-semibold;
    }

    &.success {
        @apply text-success-600 font-semibold;
    }
}

.codepay-countdown-timer {
    &.warning {
        @apply text-danger-600 font-semibold;
    }
}

.swiper-slide {
    &.home-highlight-game-slide {
        @apply max-h-full sm:max-w-max sm:max-h-max xl:max-w-[calc(32.25%_-_6px)] xl:max-h-[184px];
    }
}

#middleView-playerDiv {
    @apply z-[6] #{!important};
}

.no-scroll {
    @apply overflow-hidden xl:pr-1.5;
}

.status-history-reverse {
    @apply xl:top-full;
}

.shadow-shadow-status-list-reverse {
    @apply xl:bottom-[calc(100%_-_16px)];
}

#generic-modal {
    &:has(#error-login-modal) {
        @apply z-[53];
    }
}

#chat-widget-container {
    @apply z-[71] xl:bottom-[74px] #{!important};

    #chat-widget-minimized {
        @apply hidden #{!important};
    }

    &.has-modal {
        @apply z-[100] #{!important};
    }
}

#chat-widget-minimized {
    @apply hidden;
}

c2-minigame {
    div[draggable="true"] {
        @apply z-[53] #{!important};
    }
}

@keyframes scroll {
    from {
        -webkit-transform: translate(0, 0px);
    }
    65% {
        -webkit-transform: translate(0, 10px);
    }
    to {
        -webkit-transform: translate(0, 0);
    }
}

.home-promotion-nav {
    &.swiper-button-disabled {
        @apply bg-neutral-50;
    }
}

div[id^="middleView-"] {
    @apply z-[1] #{!important};
}

video[id^="h5live-"] {
    @apply h-full w-full absolute rounded-lg bg-transparent #{!important};
}

.page-container {
    &:has(.promotion-page) {
        @apply xl:bg-neutral-100;
    }
}

#searchKeyword {
    @apply xl:max-w-[120px];
}

.icon-user,
.icon-phone {
    @apply cursor-default #{!important};
}

@media (min-width: 700px) and (max-width: 1200px) {
    .bg-hotmatch-ct {
        @apply bg-cover aspect-[300/160];
    }
}
@media (min-width: 480px) and (max-width: 700px) {
    .bg-hotmatch-ct {
        @apply bg-cover aspect-[300/200];
    }
}

.card-list-lottery {
    .swiper-slide {
        .uicard {
            @apply overflow-hidden rounded;
            .uicard-jackpot {
                @apply rounded-b-none;
            }
            &__jackpot  {
                img {
                    @apply size-2.5 min-w-2.5 xl:size-3.5 xl:min-w-3.5;
                }
            }
            .js-jackpot-value {
                @apply text-[10px] xl:text-sm;
            }
        }
    }
}

div[id^='middleView-'] {
    @apply z-[1] pointer-events-none #{!important};
}

video[id^='h5live-'],
iframe[id^='h5live-'],
iframe[name^='h5live-'] {
    @apply h-full w-full absolute rounded-[6px] bg-transparent pointer-events-none #{!important};
}
video[id^='h5live-'] {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scaleX(1.8) scaleY(1.8) rotate(0deg)!important;
}

#h5live-vingame_sb_77783 {
    transform: translate(-50%, -38%) scaleX(2.2) scaleY(2.2) rotate(0) !important;
}

#h5live-vingame_bc_77784 {
    transform: translate(-50%, -42%) scaleX(2.1) scaleY(2.1) rotate(0) !important;
}

#h5live-vingame_bacca_77778 {
    transform: translate(-50%, -38%) scaleX(2.3) scaleY(2.3) rotate(0) !important;
}

#h5live-vingame_xd_77786 {
    transform: translate(-50%, -46%) scaleX(2) scaleY(2) rotate(0) !important;
}

#h5live-rik_vgmn_111 {
    transform: translate(-50%, -47%) scaleX(1.9) scaleY(1.9) rotate(0) !important;
}

#h5live-b52_vgmn_109,
#h5live-b52_vgmn_108,
#h5live-b52_vgmn_110 {
    transform: translate(-50%, -55%) scaleX(2) scaleY(2) rotate(0) !important;
}

#h5live-789club_G1X_305 {
    transform: translate(-50%, -50%) scaleX(2.1) scaleY(2.1) rotate(0) !important;
}

#h5live-789club_G1X_306 {
    transform: translate(-50%, -50%) scaleX(2.6) scaleY(2.6) rotate(0) !important;
}

.js-live-game-item-preview-player {
    video[id^='h5live-'],
    iframe[id^='h5live-'],
    iframe[name^='h5live-'] {
        @apply object-cover #{!important} ;
    }
    iframe[id^='h5live-'],
    iframe[name^='h5live-'] {
        @apply scale-x-105 #{!important};
    }
    video[id^='h5live-'] {
        transform: translate(-50%, -50%) scaleX(1) scaleY(1) rotate(0deg)!important;
    }

    #h5live-playerid_vingame_xd_77786 {
        transform: translate(-50%, -30%) scaleX(1.4) scaleY(1.4) rotate(0) !important;
    }

    @media (max-width: 1199px) {
        #h5live-playerid_vingame_bacca_77778 {
            transform: translate(-50%, -36%) scaleX(1.4) scaleY(1.4) rotate(0) !important;
        }
    }
}

.js-live-game-item-preview-player-zoom {
    video[id^='h5live-'] {
        transform: translate(-50%, -50%) scaleX(1.1) scaleY(1.1) rotate(0deg)!important;
    }
}

.swiper-pagination-bullet {
    @apply opacity-100 #{!important};
}

.heroSwiper {
    &.swiper-container {
        .swiper-pagination {
            .swiper-pagination-bullet {
                @apply bg-primary-525;
                &.swiper-pagination-bullet-active {
                    @apply bg-primary-500;
                }
            }
        }
    }
}

.see-more {
    &:hover {
        span {
            @apply xl:text-neutral-1000;
        }
        i {
            @apply xl:text-neutral-1000;
        }
    }
}
header {
    .menu-item {
        &:hover,&.active-link {
            div {
                img {
                    filter: brightness(0) saturate(100%) invert(25%) sepia(99%) saturate(1578%) hue-rotate(335deg) brightness(94%) contrast(97%);
                }
            }
        }
    }
}
.bg-jackpot-gradient {
    background: url("../../public/asset/images/home/<USER>/bg-jackpot.png") center center no-repeat;
    background-size: 100% 100%;
}

#submit-edit-password-btn {
    @apply mt-5 xl:mt-6 #{!important};
}

#collapse-content-add-bank {
    .form-bank {
        @apply mb-[6px];
    }
}
.js-game-card-item-live-stream div[id^=middleView-] {
    @apply pointer-events-none;
}

.mobile-overflow-hidden {
    @apply overflow-hidden xl:overflow-auto;
}

button {
    &:focus-visible {
        @apply outline-none;
    }
}

@supports (-webkit-touch-callout: none) {
    .mobile-overflow-hidden {
        @apply overflow-hidden xl:overflow-auto fixed w-full h-full overflow-y-scroll;
        -webkit-overflow-scrolling: touch;
    }

    input {
        &:focus {
            touch-action: none;
        }
    }
}

@supports (-webkit-touch-callout: none) {
    .block-scroll {
        @apply overflow-hidden fixed w-full h-full;
        -webkit-overflow-scrolling: touch;
    }

    input {
        &:focus {
            touch-action: none;
        }
    }
}

.hero-banner {
    @media (min-width: 1200px) {
        background: url("../../public/asset/images/home/<USER>/bg-hero.avif") center center no-repeat;
        background-size: cover;
    }
}

@media (min-width: 1200px) and (max-width: 1350px) {
    .status-list {
        @apply right-0 left-auto translate-x-0 #{!important};
        span {
            @apply left-auto right-[40px];
        }
    }
}

.reward-index {
    background: linear-gradient(180deg, #FF3131 22.58%, #A20000 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.reward-money {
    text-shadow: 0px 1px 0px #00000080;
}

.scrollbar-without-bg::-webkit-scrollbar {
    width: 6px;

    @media (max-width: 1200px) {
        width: 4px;
    }
}

.scrollbar-without-bg::-webkit-scrollbar-track {
    background: transparent;
}

.scrollbar-without-bg::-webkit-scrollbar-thumb {
  background: #DED7D7;
  border-radius: 45px;
}

.disabled-deposit-card {
    filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7500%) hue-rotate(244deg) brightness(109%) contrast(105%);
}

.ring-container {
    &.live {
        .circle {
            animation: blink 0.7s infinite ease-in-out;
        }

        .ringring {
            box-shadow: 0 0 1px 0 #F71B26;
            -webkit-animation: pulsate 1.5s ease-out;
            -webkit-animation-iteration-count: infinite; 
        }

        .ringring:nth-child(2) {
            animation-delay: 1.1s;
        }

        .ringring:nth-child(3) {
            animation-delay: 0.7s;
        }

        @keyframes blink {
            0% {
                transform: scale(1);
            }
            100% {
                transform: scale(1.2);
            }
        }

        @keyframes pulsate {
            0% {-webkit-transform: scale(0.9, 0.9); opacity: 0.0;}
            50% {opacity: 1.0;}
            100% {-webkit-transform: scale(2.8, 2.8); opacity: 0.0;}
        }
    }
}

.top-racing-index {
    background: linear-gradient(96.42deg, #FF0606 7.26%, #FF39A6 96.61%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.top-racing-money-gift {
    background: linear-gradient(90deg, #FFE4A9 0%, #FBEBC1 42.08%, #FFD694 56.19%, #FBEDC1 66.81%, #FFD9AE 100%),
                radial-gradient(14.66% 46.64% at 18.98% 30%, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%) /* warning: gradient uses a rotation that is not supported by CSS and may not behave as expected */,
                radial-gradient(13.16% 40.83% at 62.97% 82.5%, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%) /* warning: gradient uses a rotation that is not supported by CSS and may not behave as expected */;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: white;
    -webkit-text-stroke: 0.31px #FFFFFF;
}

.top-racing-money-gift-2 {
    text-shadow: 0px 2.2px 0px #00000080;

}

.final-club-world-cup-money-gift {
    background: linear-gradient(90deg, #D00003 0%, #9D0003 100%);

    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.rank-bottom-container[data-status="auth"] .rank-bottom-login {
    display: flex;
}

.rank-bottom-container[data-status="expire"] .rank-bottom-expire {
    display: flex;
}

.rank-bottom-container[data-status="before"] .rank-bottom-before {
    display: flex;
}

.rank-bottom-container[data-status="auth"] .rank-bottom-auth {
    display: flex;
}

.grecaptcha-badge { 
    @apply invisible;
}

.language-arrow {
    @apply transition-transform duration-300;
    &.rotate-180-custom {
      @apply rotate-180;
    }
}

.language__dropdown-content {
    @apply transition-opacity duration-300 opacity-0 invisible hidden pointer-events-none;
    &.show {
      @apply opacity-100 visible pointer-events-auto block;
    }
}

.jackpot-money {
    background: linear-gradient(145.96deg, #FFEECF 19.65%, #FFDB82 56%, #FFF2DA 92.35%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}