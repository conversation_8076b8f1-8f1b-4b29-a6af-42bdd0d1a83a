<?php
use Illuminate\Support\Str;

$footerLobbyGames = config('footer.footerBanks') ?? [];
$providerLinkList = config('footer.providerLinkList') ?? [];
$currentPath = request()->path();
$isFooterLobbyGames = in_array($currentPath, config('footer.footerLobbyGames') ?? []) || Str::contains($currentPath, ['song-bai-livecasino-truc-tuyen', 'cong-game']);
$brandName = strtolower(config('app.brand_name'));
$footerMenus = config('footer.footerMenu');
$banks = config('footer.footerBanks');
$footerSocial = config('footer.footerSocial');
$swiperConfig = [
    'loop' => true,
    'freeMode' => true,
    'spaceBetween' => 12,
    'slidesPerView' => 'auto',
    'autoplay' => [
        'delay' => 5,
        'disableOnInteraction' => false,
        'pauseOnMouseEnter' => false,
    ],
    'speed' => 3000,
    // 'loopAdditionalSlides' => 5,
    // 'watchSlidesProgress' => true,
    // 'watchSlidesVisibility' => true,
];
$swiperConfigBank = [
    'loop' => true,
    'autoplay' => [
        'delay' => 1,
        'disableOnInteraction' => false,
        'pauseOnMouseEnter' => false,
    ],
    'spaceBetween' => 20,
    'slidesPerView' => 'auto',
    'speed' => 6000,
];
?>

<footer class="hidden mt-auto xl:block w-full justify-center bg-brand-bg-footer text-neutral-850 border-t border-neutral-150">
    <?php if (!$isFooterLobbyGames): ?>
        <div class="py-[23.5px] border-b border-neutral-150">
            <div class="container">
                <x-kit.swiper :autoplay="true" :swiperConfig="$swiperConfigBank" swiperWrapperClass="autoplay-swiper"
                    swiperRequiredClass="footer-swiper-mb">
                    <!-- Slides -->
                    @for ($i = 1; $i <= 29; $i++)
                        <div class="swiper-slide cursor-pointer !w-[116px]">
                            <a href="{{ $providerLinkList[$i - 1] }}">
                                <img src="{{ asset('asset/icons/layout/footer/providers/' . $i . '.avif') }}" alt="provider"
                                class="h-[40px] w-[116px] object-contain rounded-[4px]" loading="lazy" />
                            </a>
                        </div>
                    @endfor
                </x-kit.swiper>
            </div>
        </div>
    <?php endif; ?>
    <div <?php if ($isFooterLobbyGames): ?> class="container pt-[23px] flex flex-col items-center" <?php else: ?>
        class="container pt-6" <?php endif; ?>>

        <?php if ($isFooterLobbyGames): ?>
            <div class="flex justify-between pb-[23.5px] w-full border-b border-neutral-150">
                <div class="flex items-center justify-start">
                    <h4 class="font-medium border-l-[3px] border-primary-500 pl-2 pr-6 text-neutral-1000">{{ __($footerMenus[0]['title']) }}
                    </h4>
                    <ul class="flex items-center gap-6 text-sm">
                        @foreach ($footerMenus[0]['child'] as $item)
                        <li class="cursor-pointer pl-2">
                            @if($item['id'] == 151617)
                                <button
                                    type="button"
                                    onclick="handleRedirectFooter({{ json_encode($item) }})"
                                    aria-label="{{ __($item['title']) }}"
                                    class="flex font-semibold text-neutral-600 hover:text-neutral-850 xl:font-normal xl:text-sm">
                                    <h4>{{ __($item['title']) }}</h4>
                                </button>
                            @else
                                <a
                                    href="{{ $item['url'] }}"
                                    aria-label="{{ __($item['title']) }}"
                                    class="flex font-semibold text-neutral-600 hover:text-neutral-850 xl:font-normal xl:text-sm">
                                    <h4>{{ __($item['title']) }}</h4>
                                </a>
                            @endif
                        </li>
                        @endforeach
                    </ul>
                </div>
                <div class="flex">
                    <div class="pr-6">
                        <h4 class="font-medium border-l-[3px] border-primary-500 pl-2 text-neutral-1000">{{ __('footer.contact') }}</h4>
                    </div>
                    <div class="flex gap-2 relative">
                        @foreach($footerSocial as $item)
                            @if($item['icon'] == 'livechat')
                                <button onclick="openLiveChat()" class="text-secondary-500">
                                    <img src="{{ asset('asset/icons/layout/footer/logo/games/' . $item['icon'] .'.avif') }}" alt="{{ $item['title'] }}" width="200"
                                        class="size-[24px] object-contain" loading="lazy" />
                                </button>
                            @else
                                <a href="{{ $item['link'] }}"
                                    target="{{ $item['newTab'] ? '_blank' : '_self' }}">
                                    <img src="{{ asset('asset/icons/layout/footer/logo/games/' . $item['icon'] .'.avif') }}" alt="{{ $item['title'] }}" width="200"
                                        class="size-[24px] object-contain" loading="lazy" />
                                </a>
                            @endif
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="pt-[23.5px] pointer-events-none container">
                <x-kit.swiper :autoplay="true" :swiperConfig="$swiperConfigBank" swiperRequiredClass="footer-swiper-1"
                    swiperWrapperClass="autoplay-swiper">
                    <!-- Slides -->
                    @foreach ($banks as $bank)
                    <div class="swiper-slide !size-[44px] !pr-0 bg-neutral p-[6px] rounded-full">
                        <img src="{{ asset('asset/icons/layout/footer/banks/' . $bank) }}" alt="{{ $bank }}"
                            class="size-[32px]" loading="lazy" />
                    </div>
                    @endforeach
                </x-kit.swiper>
            </div>

            <div class="flex flex-col justify-center items-center max-w-[894px] py-6">
                <a href="/">
                    <img src="{{ asset('asset/images/brand/logo.svg') }}"
                        class="mb-6 h-auto w-[138px] max-w-full object-contain object-left xl:h-[35px] xl:w-[136px]"
                        loading="lazy" />
                </a>
                <div class="flex text-sm pr-[45px]">
                    <div class="text-xs px-[2rem] leading-[18px] text-center">
                        {{ __('footer.license') }}<a href="/ve-chung-toi"
                            class="text-secondary-500 pl-1 cursor-pointer">{{ __('footer.seemore') }}</a>
                    </div>
                </div>
            </div>


        <?php else: ?>
            <div class="gap-[32px] grid grid-cols-[350fr_194fr_195fr_194fr_180fr] pb-[23.5px]">
                <div class="w-full xl:w-auto">
                    <a href="/">
                        <img src="{{ asset('asset/images/brand/logo.svg') }}" alt="footer logo"
                            class="mb-6 h-auto w-[138px] max-w-full object-contain object-left xl:h-[35px] xl:w-[136px]"
                            loading="lazy" />
                    </a>
                    <div class="flex text-sm pr-[45px]">
                        <div class="leading-5 max-w-[303px]">
                            {{ __('footer.license') }}<a href="/ve-chung-toi"
                                class="text-secondary-500 pl-1 cursor-pointer">{{ __('footer.seemore') }}</a>
                        </div>
                    </div>
                </div>
                @foreach ($footerMenus as $menu)
                <div class="footer-menu-group">
                    <h4 class="font-medium border-l-[3px] border-primary-500 pl-2 text-neutral-1000 capitalize">{{ __($menu['title']) }}</h4>
                    <ul class="flex flex-col gap-5 text-sm pt-5">
                        @foreach ($menu['child'] as $item)
                        <li class="cursor-pointer pl-2">
                            @if($item['id'] == 151617)
                            <button
                                type="button"
                                onclick="handleRedirectFooter({{ json_encode($item) }})"
                                aria-label="{{ __($item['title']) }}"
                                class="flex font-semibold text-neutral-850 hover:text-neutral-600 xl:font-normal xl:text-sm">
                                <h4>{{ __($item['title']) }}</h4>
                            </button>
                            @else
                                <a
                                    href="{{ $item['url'] }}"
                                    aria-label="{{ __($item['title']) }}"
                                    class="flex font-semibold text-neutral-850 hover:text-neutral-600 xl:font-normal xl:text-sm">
                                    <h4>{{ __($item['title']) }}</h4>
                                </a>
                            @endif
                        </li>
                        @endforeach
                    </ul>
                </div>
                @endforeach

                <div class="">
                    <h4 class="font-medium border-l-[3px] border-primary-500 pl-2 text-neutral-1000 capitalize">{{ __('footer.contact') }}</h4>
                    <div class="flex flex-col gap-1 pt-5 font-medium">
                        @foreach($footerSocial as $key => $item)
                            @if($item['icon'] == 'livechat')
                                <button
                                    onclick="openLiveChat()"
                                    class="flex items-center gap-1.5 bg-neutral-1550 text-neutral rounded-full p-2 text-nowrap xl:hover:!bg-btn-contact-hover"
                                    style="background-color: {{ $item['color'] }}"
                                >
                                    <img
                                        src="{{ asset('asset/icons/layout/footer/logo/' . $item['icon'] . '.avif') }}"
                                        alt="i-{{ $key }}"
                                        width="200"
                                        class="h-[24px] w-[24px] object-contain" loading="lazy"
                                    />
                                    <span class="text-xs">{{ __($item['title']) }}</span>
                                </button>
                            @else
                                <a
                                    href="{{ $item['link'] }}"
                                    target="{{ $item['newTab'] ? '_blank' : '_self' }}"
                                    class="flex items-center gap-1.5 bg-neutral-1600 text-neutral rounded-full p-2 text-nowrap xl:hover:!bg-btn-contact-hover"
                                    style="background-color: {{ $item['color'] }}"
                                >
                                    <img
                                        src="{{ asset('asset/icons/layout/footer/logo/' . $item['icon'] . '.avif') }}"
                                        alt="i-{{ $key }}"
                                        width="200"
                                        class="h-[24px] w-[24px] object-contain" loading="lazy"
                                    />
                                    <span class="text-xs">{{ __($item['title']) }}</span>
                                </a>
                            @endif
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="py-[23.5px] border-t border-neutral-150 pointer-events-none">
                <x-kit.swiper :autoplay="true" :swiperConfig="$swiperConfig" swiperWrapperClass="autoplay-swiper"
                    swiperRequiredClass="swiper-first-bank">
                    <!-- Slides -->
                    @for ($i = 23; $i >= 1; $i--)
                    <div class="swiper-slide !w-auto">
                        <div class="flex flex-col">
                            <img src="{{ asset('asset/icons/layout/footer/banks_text/' . $i . '.svg') }}"
                            alt="{{ $i }}" class="h-[44px] object-contain rounded-[4px]" loading="lazy" />
                        </div>
                    </div>
                    @endfor
                </x-kit.swiper>
                <div class="mt-[20px]">
                    <x-kit.swiper :autoplay="true" :swiperConfig="$swiperConfig" swiperWrapperClass="autoplay-swiper"
                        swiperRequiredClass="swiper-second-bank">
                        <!-- Slides -->
                        @for ($i = 22; $i >= 1; $i--)
                        <div class="swiper-slide !w-auto">
                            <div class="flex flex-col">
                                <img src="{{ asset('asset/icons/layout/footer/banks_text/' . ($i + 22) . '.svg') }}"
                                alt="{{ $i + 22 }}" class="h-[44px] object-contain rounded-[4px]" loading="lazy" />
                            </div>
                        </div>
                        @endfor
                    </x-kit.swiper>
                </div>
            </div>

<?php endif; ?>
</div>

<div class="container w-full flex justify-center py-6 border-t border-neutral-150">
    <p class="text-neutral-600 text-sm">
        {{ __('footer.copyright', ['brandName' => strtoupper($brandName)]) }}
    </p>
</div>
<?php if (isset($localBusiness)): ?>
    @push('scripts')
    <script>
        <?= $localBusiness->toScript() ?>
    </script>
    @endpush
<?php endif; ?>
</footer>
@push('scripts')
<script>
    const handleRedirectFooter = (item) => {
        if (item?.id === 151617) {
            const bankListModal = `<x-ui.modal :id="'bank-list-modal'" >
                                    <x-ui.bank-list-modal></x-ui.bank-list-modal>
                                </x-ui.modal>`
            openModal(bankListModal, false, 'bank-list-modal')
        }
        if (!item?.url) return
        window.location.href = item.url
    }
</script>
