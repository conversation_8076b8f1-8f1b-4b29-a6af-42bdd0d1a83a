@php
    $navList = config('header.navList');
    $mbHiddenLinks = config('header.mbHiddenLinks');

    $isOverview = request()->url() === route('en.account.index') 
                && request()->get('tab') === 'overview';
                
    $isHideMbHeader = in_array(Route::currentRouteName(), $mbHiddenLinks) || $isOverview;
@endphp

<header class="sticky top-0 z-[52] bg-neutral xl:border-b-0 border-b border-neutral-150 {{$isHideMbHeader ? 'hidden xl:block' : 'block'}} " x-data="{}">
    <div class="container {{$isHideMbHeader ? 'hidden xl:flex' : 'flex'}} justify-between items-center gap-[20px] h-[48px] py-[10px] mx-auto xl:h-[72px] xl:py-4">
        <a href='/'>
            <img alt="logo" src="{{ asset('asset/images/brand/logo.svg') }}" class="w-[100px] h-[32px] xl:w-[145px] xl:h-[46px]"/>
        </a>
        @if (Auth::check())
            <div class="flex gap-[10px] xl:gap-3 items-center">
                <x-kit.badges></x-kit.badges>
                <div class="flex cursor-pointer gap-2 h-[28px] max-w-[135px] py-[4px] pl-[8px] pr-[7px] bg-neutral-150 xl:hover:bg-neutral-200 rounded-[6px] xl:rounded-[8px] xl:h-[40px] xl:p-[4px]">
                    <a href="/account" aria-label="account" class="hidden h-[32px] w-[32px] rounded-lg xl:block">
                        <img alt="avatar" src="{{ asset('asset/images/header/avatar-header.avif') }}" class="w-full h-full"/>
                    </a>
                    <div class="flex items-center gap-2 xl:w-[calc(100%-40px)]">
                        <div class="flex flex-col w-full">
                            <a href="/account" aria-label="account" class="js-fullname-account hidden max-w-full text-[12px] truncate leading-[18px] font-normal text-neutral-850 line-clamp-1 xl:block">
                                {{ Auth::user()->fullname ?? (Auth::user()->username ?? '') }}
                            </a>
                            <a href="/account" aria-label="account" class="js-account-balance text-[12px] leading-[18px] font-medium text-primary-700">
                                {{ number_format(Auth::user()->balance ?? 0, 0, '.', ',') }} K
                            </a>
                        </div>
                        <a href="/account/deposit" class="flex justify-center items-center min-w-[20px] h-[20px] bg-secondary-400 rounded-full cursor-pointer xl:hidden">
                            <i class="icon-plus-11969 text-[14px] text-neutral"></i>
                        </a>
                    </div>
                </div>
                <x-kit.button aria-label="nap tien" onclick="window.location.href='/account/deposit'" button-type="button" style="filled"
                    type="secondary" size="large" class="hidden min-w-[96px] rounded-full xl:flex">{{ __('header.menus.deposit') }}</x-kit.button>

                <div class="h-[23px] w-[1px] bg-neutral-300 xl:block hidden"></div>

                <div class="language items-center cursor-pointer h-full">
                    <div class="js-language-dropdown language__dropdown z-10 flex items-center relative gap-0.5 min-w-[52px] w-[52px] h-[32px]" data-target="#languageOption">
                        <div class="language__flag w-[32px] h-[32px] min-w-[32px] min-h-[32px]">
                            <img src="{{ asset('asset/images/header/flag-'.Config::get('app.locale').'.svg') }}" alt="flag-{{ Config::get('app.locale') }}" class="w-full h-full" />
                        </div>
                        <img src="{{ asset('asset/images/header/arrow-down.svg') }}" alt="arrow-down" class="js-language-arrow language-arrow w-[18px] h-[18px]" />
                        <div class="language__dropdown-content absolute top-[48px] right-0 w-[157px] h-[100px] p-2 border border-neutral-150 rounded-md bg-neutral" id="languageOption">
                            <div
                                @class([
                                    'language__dropdown-item flex items-center gap-2 h-10 rounded px-2 cursor-pointer',
                                    'active-link bg-neutral-150' => Config::get('app.locale') === 'vi',
                                ])
                            >
                                <img src="{{ asset('asset/images/header/flag-vi.svg') }}" alt="flag-vi" class="w-[32px] h-[32px]" />
                                <a href="{{ route('change-language', ['lang' => 'vi']) }}" class="text-[12px] leading-[18px] font-medium text-sm text-neutral-1000">Tiếng Việt</a>
                            </div>
                            <div
                                class="language__dropdown-item flex items-center gap-2 h-10 rounded px-2 cursor-pointer"
                                @class([
                                    'active-link bg-neutral-150' => Config::get('app.locale') === 'en',
                                ])
                            >
                                <img src="{{ asset('asset/images/header/flag-en.svg') }}" alt="flag-en" class="w-[32px] h-[32px]" />
                                <a href="{{ route('change-language', ['lang' => 'en']) }}" class="text-[12px] leading-[18px] font-medium text-sm text-neutral-1000">English</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="flex gap-3 items-center">
                <div class="flex gap-[8px] xl:hidden">
                    <x-kit.button button-type="button" onclick="openLogin()" style="out-line" type="primary" size="small" class="min-w-[88px] text-primary-700 border border-primary-700 rounded-full capitalize">{{ __('header.menus.login') }}</x-kit.button>
                    <x-kit.button button-type="button" onclick="openSignup()" style="filled" type="secondary" size="small" class="min-w-[88px] rounded-full capitalize">{{ __('header.menus.register') }}</x-kit.button>
                </div>
                <form id="login-form" class="hidden gap-4 xl:flex">
                    @csrf
                    <div class="flex gap-2">
                        <x-kit.button button-type="button" onclick="openLogin()" style="out-line" type="primary" size="large"
                            class="min-w-[111px] rounded-full text-primary-700 border-2 border-primary-700 xl:hover:border-primary-400 capitalize">{{ __('header.menus.login') }}</x-kit.button>
                        <x-kit.button button-type="button" onclick="openSignup()" style="filled" type="secondary" size="large"
                            class="js-btn-login-register rounded-full min-w-[92px] capitalize">{{ __('header.menus.register') }}</x-kit.button>
                    </div>
                </form>
                <div class="h-[23px] w-[1px] bg-neutral-300 xl:block hidden"></div>

                <div class="language items-center cursor-pointer h-full">
                    <div class="js-language-dropdown language__dropdown z-10 flex items-center relative gap-0.5 min-w-[52px] w-[52px] h-[32px]" data-target="#languageOption">
                        <div class="language__flag w-[32px] h-[32px] min-w-[32px] min-h-[32px]">
                            <img src="{{ asset('asset/images/header/flag-'.Config::get('app.locale').'.svg') }}" alt="flag-{{ Config::get('app.locale') }}" class="w-full h-full" />
                        </div>
                        <img src="{{ asset('asset/images/header/arrow-down.svg') }}" alt="arrow-down" class="js-language-arrow language-arrow w-[18px] h-[18px]" />
                        <div class="language__dropdown-content absolute top-[48px] right-0 w-[157px] h-[100px] p-2 border border-neutral-150 rounded-md bg-neutral" id="languageOption">
                            <div
                                @class([
                                    'language__dropdown-item flex items-center gap-2 h-10 rounded px-2 cursor-pointer',
                                    'active-link bg-neutral-150' => Config::get('app.locale') === 'vi',
                                ])
                            >
                                <img src="{{ asset('asset/images/header/flag-vi.svg') }}" alt="flag-vi" class="w-[32px] h-[32px]" />
                                <a href="{{ route('change-language', ['lang' => 'vi']) }}" class="text-[12px] leading-[18px] font-medium text-sm text-neutral-1000">{{ __('header.menus.vietnamese') }}</a>
                            </div>
                            <div
                                class="language__dropdown-item flex items-center gap-2 h-10 rounded px-2 cursor-pointer"
                                @class([
                                    'active-link bg-neutral-150' => Config::get('app.locale') === 'en',
                                ])
                            >
                                <img src="{{ asset('asset/images/header/flag-en.svg') }}" alt="flag-en" class="w-[32px] h-[32px]" />
                                <a href="{{ route('change-language', ['lang' => 'en']) }}" class="text-[12px] leading-[18px] font-medium text-sm text-neutral-1000">{{ __('header.menus.english') }}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
    <div class="hidden justify-center w-full h-[60px] bg-header-gradient xl:flex">
        <div class="container flex justify-between gap-4 w-full h-full max-[1250px]:gap-2">
            @foreach ($navList as $navItemIndex => $navItem)
                @if (isset($navItem['activeList']))
                    @php
                        $isActive = (in_array(request()->getPathInfo(), $navItem['activeList']) && $navItemIndex !== count($navList) - 1) || ($navItemIndex === count($navList) - 1 && (request()->getPathInfo() === $navItem['href'] || in_array(request()->getPathInfo(), $navItem['activeList'])));
                    @endphp
                    <div 
                        @class([
                            '[&:hover_.nav-list]:h-[145px] [&:hover_.nav-list]:pt-[23px] menu-item',
                            'active-link font-medium' => $isActive,
                        ])
                    >

                        @if (isset($navItem['href']) && $navItem['href'])
                            <a 
                                class="relative flex items-center px-[10px] h-full text-[16px] leading-[24px] font-medium text-neutral-1000 cursor-pointer [.active-link_&]:bg-header-gradient-menu [.menu-item:hover_&]:bg-header-gradient-menu"
                                href="{{ $navItem['href'] }}" >
                                <div class="flex flex-nowrap items-center gap-1 [.active-link_&]:text-secondary-500 [.menu-item:hover_&]:text-secondary-500">
                                    <img src="{{ asset($navItem['icon']) }}" alt="icon-nav" class="size-[24px]">
                                    <div class="text-[16px] leading-[24px] font-medium text-nowrap">{{ __($navItem['label']) }}</div>
                                </div>
                                <span class="absolute bottom-0 left-1/2 -translate-x-1/2 hidden w-[30px] h-[5px] bg-secondary-500 [.active-link_&]:block [.menu-item:hover_&]:block"></span>
                                <x-kit.label type="{{ $navItem['labelType'] }}" size="large" class="absolute top-0 right-[-5px]" hotSize="true"></x-kit.label>
                            </a>

                        @else
                            <div
                                class="relative flex items-center px-[10px] h-full text-[16px] leading-[24px] font-medium text-neutral-1000 cursor-pointer [.active-link_&]:bg-header-gradient-menu [.menu-item:hover_&]:bg-header-gradient-menu"
                            >
                                <div class="flex flex-nowrap items-center gap-1 [.active-link_&]:text-secondary-500 [.menu-item:hover_&]:text-secondary-500">
                                    <img src="{{ asset($navItem['icon']) }}" alt="icon-nav" class="size-[24px]">
                                    <div class="text-[16px] leading-[24px] font-medium text-nowrap">{{ __($navItem['label']) }}</div>
                                </div>
                                    <span class="absolute bottom-0 left-1/2 -translate-x-1/2 hidden w-[30px] h-[5px] bg-secondary-500 [.active-link_&]:block [.menu-item:hover_&]:block"></span>
                                <x-kit.label type="{{ $navItem['labelType'] }}" size="large" class="absolute top-0 right-[-5px]" hotSize="true"></x-kit.label>
                            </div> 
                        @endif

                        @if (isset($navItem['list']))
                            <div class="nav-list absolute top-[132px] left-0 flex justify-center items-start z-30 gap-[60px] w-full h-0 pt-0 bg-neutral-70 backdrop-blur-[3px] overflow-hidden transition-all duration-300">
                                @foreach ($navItem['list'] as $item)
                                    <button
                                        type="button"
                                        onclick="openGameHeaderItem('{{ $item['link'] }}')"
                                        aria-label="{{ __($item['label']) }}"
                                        @class([
                                            'relative flex flex-col justify-center items-center gap-[2px] min-w-[100px] h-[100px] px-[3px] rounded-[8px] overflow-hidden cursor-pointer [&:hover_.nav-active-bg]:block [&.active-link_.nav-active-bg]:block',
                                            'active-link' => request()->getPathInfo() === $item['link'],
                                        ])
                                    >
                                        <img src="{{ asset($item['icon']) }}" alt="icon-nav" class="relative z-[1] w-[70px] h-[70px] submenu-item"/>
                                        <div class="relative z-[1] text-[14px] leading-[20px] uppercase font-medium text-nowrap text-neutral">
                                            {{ __($item['label']) }}
                                        </div>
                                    </button>
                                @endforeach
                            </div>
                        @endif
                    </div>
                @else
                    <a 
                        href="{{ $navItem['href'] }}"
                        @class([
                            '[&:hover_.nav-list]:flex menu-item',
                            'active-link font-medium' => request()->getPathInfo() === $navItem['href'],
                        ])
                    >
                        <div class="relative flex items-center px-[10px] h-full text-[16px] leading-[24px] font-medium text-neutral-1000 cursor-pointer [.active-link_&]:bg-header-gradient-menu [.menu-item:hover_&]:bg-header-gradient-menu">
                            <div class="flex items-center gap-1 [.active-link_&]:text-secondary-500 [.menu-item:hover_&]:text-secondary-500">
                                <img src="{{ asset($navItem['icon']) }}" alt="icon-nav" class="size-[24px]">
                                <div class="text-[16px] leading-[24px] font-medium text-nowrap">{{ __($navItem['label']) }}</div>
                            </div>
                            <span class="absolute bottom-0 left-1/2 -translate-x-1/2 hidden w-[30px] h-[5px] bg-secondary-500 [.active-link_&]:block [.menu-item:hover_&]:block"></span>
                            <x-kit.label type="{{ $navItem['labelType'] }}" size="large" class="absolute top-0 right-[-5px]" hotSize="true"></x-kit.label>
                        </div>
                    </a>
                @endif
            @endforeach
        </div>
    </div>
</header>
<div id="generic-modal"></div>
<div id="auth-modal"></div>
<div id="noti-modal"></div>

@push('scripts')
    <script>
        window.addEventListener("DOMContentLoaded", (event) => {
            $('.js-username-login input, .js-password-login input').on('input', function() {
                const username = $('.js-username-login input').val();
                const password = $('.js-password-login input').val();
            });
            $('#login-form').on('submit', async function(e) {
                e.preventDefault();
                const username = $('.js-username-login input').val();
                const password = $('.js-password-login input').val();
               
                if (!username || !password) {
                    openModal(errorLoginModal, false, 'error-login-modal');
                    $('#error-login-text').text('{{ __('pages.auth.please_enter_credentials') }}');
                } else {
                    const token = await handleRecaptcha();
                    const payload = {
                        username: username,
                        password: password,
                        token: token
                    };
                    if (window.dataLayer && Array.isArray(window.dataLayer)) {
                        window.dataLayer.push({ event: "formSubmitted", formName: "Form_Login" });
                    }
                    
                    res = await submitData(url = '/login', params = payload, apiBase = '', ver = '');
                    if (res.status) {
                        useToast('success', res?.message);

                        // update flow login - giu nguyen trang truoc dang nhap
                        setTimeout(() => window.location.reload(), 1500);

                    } else {
                        openModal(errorLoginModal, false, 'error-login-modal');

                        $('#error-login-text').text(res?.message)
                    }
                }
            });
        });

        const loginModal = `<x-ui.auth-modal :id="'login-modal'"><x-ui.auth.login-form></x-ui.auth.login-form></x-ui.auth-modal>`
        const signupModal = `<x-ui.auth-modal :id="'signup-modal'"><x-ui.auth.signup-form></x-ui.auth.signup-form></x-ui.auth-modal>`
        const forgetModal = `<x-ui.auth-modal :id="'forget-modal'"><x-ui.auth.forget-form></x-ui.auth.forget-form></x-ui.auth-modal>`
        const newPassModal = `<x-ui.auth-modal :id="'new-pass-modal'"><x-ui.auth.new-pass-form></x-ui.auth.new-pass-form></x-ui.auth-modal>`
        const errorLoginModal = `<x-ui.modal :id="'error-login-modal'"><x-ui.auth.error-login type='error'></x-ui.auth.error-login></x-ui.modal>`;
        const errorLoginBlockModal = `<x-ui.modal :id="'error-login-modal'"><x-ui.auth.error-login type='block'></x-ui.auth.error-login></x-ui.modal>`;
        const errorLoginNotfoundModal = `<x-ui.modal :id="'error-login-modal'"><x-ui.auth.error-login type='notfound'></x-ui.auth.error-login></x-ui.modal>`;
        const changeNameModal = `<x-ui.modal :id="'change-name-modal'"><x-ui.auth.change-name></x-ui.auth.change-name></x-ui.modal>`;
        const notiSignupModal = `<x-ui.modal :id="'noti-signup-modal'"><x-ui.auth.noti-signup></x-ui.auth.noti-signup></x-ui.modal>`;
        const rewardEventModal = `<x-ui.reward-event-modal></x-ui.reward-event-modal>`;
        const topRacingEventModal = `<x-ui.top-racing-event-modal></x-ui.top-racing-event-modal>`;
        const topRacingEventGiftModal = `<x-ui.event-modal.top-racing :id="'top-racing-gift-modal'"></x-ui.event-modal.top-racing>`;
        const rewardGoldenHourGiftModal = `<x-ui.event-modal.reward id="reward-golden-hour-gift-modal"><x-ui.event-modal.reward.gift></x-ui.event-modal.reward.gift></x-ui.event-modal.reward>`
        const rewardGoldenHourResultModal = `<x-ui.event-modal.reward id="reward-golden-hour-result-modal"><x-ui.event-modal.reward.result></x-ui.event-modal.reward.result></x-ui.event-modal.reward>`;
        const rewardGoldenHourExpireModal = `<x-ui.event-modal.reward id="reward-golden-hour-expire-modal"><x-ui.event-modal.reward.expire></x-ui.event-modal.reward.expire></x-ui.event-modal.reward>`;
        const finalClubWorldCupModal = `<x-ui.final-club-world-cup-event-modal></x-ui.final-club-world-cup-event-modal>`;
        const finalClubWorldCupInfoModal = `<x-ui.event-modal.final-club-world-cup></x-ui.event-modal.final-club-world-cup>`;
        const finalClubWorldCupGiftModal = `<x-ui.event-modal.final-club-world-cup.gift></x-ui.event-modal.final-club-world-cup.gift>`;
    </script>
@endpush
