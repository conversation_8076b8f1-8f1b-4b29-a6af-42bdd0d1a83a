<!-- style: filled/out-line/ghost -->
<!-- type: primary/secondary/tertiary -->
<!-- size: small/medium/large -->

@props([
    'style' => 'filled',
    'type' => 'primary',
    'size' => 'large',
    'loading' => false,
    'disabled' => false,
    'class' => null,
    'leftIcon' => null,
    'rightIcon' => null,
    'symbol' => null,
    'buttonType' => 'button',
    'link' => null,
])

<?php
$getClassStyle = function ($style, $type) {
    if ($style === 'filled') {
        if ($type === 'primary') {
            return 'text-neutral bg-primary-500 xl:hover:bg-primary-600 disabled:!bg-primary-100 disabled:!text-neutral-600';
        } elseif ($type === 'secondary') {
            return 'text-neutral bg-secondary-500 xl:hover:bg-secondary-700 disabled:!bg-secondary-300';
        }  elseif ($type === 'primary-700') {
            return 'text-neutral bg-primary-700 xl:hover:bg-primary-600 disabled:!bg-primary-100 disabled:!text-neutral-600';
        } else {
            return 'text-neutral-800 bg-neutral-100 xl:hover:bg-neutral-200 xl:hover:text-neutral-1000 disabled:!text-neutral-600 text-nowrap disabled:!bg-neutral-50 [&:disabled_i]:text-neutral-300';
        }
    } elseif ($style === 'out-line') {
        if ($type === 'primary') {
        return 'text-primary-500 border border-primary-500 xl:hover:border-primary-600 xl:hover:text-primary-600 disabled:!text-danger-300 disabled:!border-danger-300';
        } elseif ($type === 'secondary') {
            return 'text-secondary-500 border border-secondary-500 xl:hover:border-secondary-600 xl:hover:text-secondary-600 disabled:!text-secondary-300 disabled:!border-secondary-300';
        } else {
            return 'text-neutral-800 border border-neutral-150 xl:hover:border-neutral-200 xl:hover:text-neutral-1000 disabled:!text-neutral-600 disabled:!border-neutral-150 [&:disabled_i]:text-neutral-300';
        }
    } else {
        if ($type === 'primary') {
            return 'text-primary-500 xl:hover:text-primary-600 disabled:!text-primary-50 disabled:!bg-primary-100';
        } elseif ($type === 'secondary') {
            return 'text-secondary-500 xl:hover:text-secondary-600 disabled:!text-secondary-300';
        } else {
            return 'text-neutral-800 xl:hover:text-neutral-1000 disabled:!text-neutral-600 [&:disabled_i]:text-neutral-300';
        }
    }
};

$getClassSize = function ($size) {
    if ($size === 'large' || $size === 'medium') {
        $classFont = 'text-[14px] leading-[20px] font-medium [&_i]:text-[20px]';

        if ($size === 'large') {
            return $classFont .= ' h-[40px] py-[10px] px-[16px]';
        }
        return $classFont .= ' h-[32px] py-[6px] px-[11px]';
    }

    return 'h-[28px] py-[5px] px-[11px] text-[12px] leading-[18px] font-medium [&_i]:text-[14px]';
};

$getClassSymbol = function ($symbol, $size, $style, $type) {
    $newSize = '';
    if ($symbol) {
        if ($size === 'large') {
            $newSize = 'w-[40px] h-[40px] p-0';
        } elseif ($size === 'medium') {
            $newSize = 'w-[32px] h-[32px] p-0';
        } else {
            $newSize = 'w-[24px] h-[24px] p-0';
        }

        if ($style === 'filled' && ($type = 'tertiary')) {
            $newSize .= ' text-primary-500 xl:hover:text-primary-500';
        }

        return $newSize;
    }
};

$classes = ['flex justify-center items-center gap-[6px] w-max rounded-[8px] whitespace-nowrap max-[387px]:tracking-[-0.3px] disabled:cursor-not-allowed xl:transition-all', 'pointer-events-none relative' => $loading, $getClassSize($size), $getClassStyle($style, $type), $getClassSymbol($symbol, $size, $style, $type), $class];

$className = twMerge(flatten_classes($classes));
?>


@if($link)
    <a href="{{ $link }}"
@else
    <button 
@endif 
    type="{{ $buttonType }}" {{ $attributes }} @disabled($disabled || $loading) class="{{ $className }}"
>
    @if (!$symbol)
        @if ($leftIcon)
            <i class="{{ $leftIcon }} flex justify-center items-center pointer-events-none"></i>
        @endif
        {{ $slot }}

        @if ($rightIcon)
            <i class="{{ $rightIcon }} flex justify-center items-center pointer-events-none"></i>
        @endif
    @else
        <i class="{{ $symbol }} pointer-events-none"></i>
    @endif
@if ($link)
    </a>
@else 
    </button>
@endif
