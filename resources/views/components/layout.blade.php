@props([
    'class' => '',
    'hiddenBottomBar' => false,
    "classBg"=>'',
    'forbidden' => false,
    'isIframe' => false
])

@php
    use App\Helpers\DetectDeviceHelper;
    $isMobile = DetectDeviceHelper::isMobile();
    $position = "70vh 55px auto auto";

    if ($isMobile) {
        $position = "65vh 0px auto auto";
    }
    $GOOGLE_RECAPTCHA_SCRIPT = config('app.recaptcha.script');
@endphp
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    {{-- Icon --}}
    <link rel="icon" href="{{ asset('asset/images/brand/favicon.svg') }}" type="image/svg+xml">

    <!-- SEO -->
    @isset($seo)
        @if ($seo)
            <title>{{ $seo->title }}</title>
            <meta name="title" content="{{ $seo->title }}" />
            <meta name="description" content="{{ $seo->description ?? '' }}" />
            <meta name="keywords" content="{{ $seo->keywords ?? '' }}">
            <link rel="canonical" href="@yield('canonical', url()->current())">
            <meta property="og:title" content="{{ $seo->title ?? '' }}">
            <meta property="og:description" content="{{ $seo->description ?? '' }}">
            <meta property="og:type" content="website">
            <meta property="og:url" content="{{ url()->current() }}">
            <meta property="og:image" content="{{ asset('asset/images/brand/favicon.svg') }}" />
            <meta name="twitter:title" content="{{ $seo->title }}">
            <meta name="twitter:description" content="{{ $seo->description ?? '' }}">
            <meta name="twitter:image" content="{{ asset('asset/images/brand/favicon.svg') }}">
            <meta name="twitter:card" content="summary_large_image" />
            @if (isset($seo->schemas))
                @foreach ($seo->schemas as $jsonLdItem)
                    <script type="application/ld+json">
                        {!! $jsonLdItem !!}
                    </script>
                @endforeach
            @endif
        @endif
    @else
        <title>{{ env('BRAND_NAME') }}</title>
        <meta name="title" content="{{ env('BRAND_NAME') }}" />
        <meta name="description" content="{{ env('BRAND_NAME') }}" />
    @endisset

    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="google-site-verification" content="{{ env('GOOGLE_SITE_VERIFICATION') }}" />


    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans&family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap&family=Mulish:ital,wght@0,200..1000;1,200..1000&display=swap" rel="stylesheet">
    <link href="{{ asset('fonts/icomoon/style.css') }}" rel="stylesheet">


    @if (request()->url() === route('en.mega645.index') || request()->url() === route('en.power655.index'))
        <script src="https://assets.vgjt.info/js/vietlott.js" defer type="module"></script>
    @endif

    @if (request()->url() === route('en.md5.index'))
        <script src="https://assets.vgjt.info/js/ldmd5.js" defer type="module"></script>
    @endif
    <!-- Styles -->
    @vite(['resources/sass/app.scss', 'resources/js/app.js'])

    @stack('sass')

    @stack('preloadLink')
</head>

<body @class(['font-sans antialiased', 'bg-neutral-100'=> str_starts_with(request()->path(), 'account'), $class])>
    <input type="hidden" id="locale" value="{{ App::getLocale() }}">
    @if (!$forbidden && !(isset($isIframe) && $isIframe && DetectDeviceHelper::isMobile()))
    <x-header></x-header>
    @endif
    <div @class([ 'container'=> in_array(request()->path(), config('execeptContainer', [])),'flex flex-col flex-grow
        page-container',$classBg])>
        {{-- SEO --}}
        @if (isset($seo) && !empty($seo->heading))
            <h1 class="hidden">{{$seo->heading}}</h1>
        @endif

        {{ $slot }}
    </div>

    @if (!$forbidden && !(isset($isIframe) && $isIframe && DetectDeviceHelper::isMobile()))
    @if (!$hiddenBottomBar)
    <x-ui.bottom-bar></x-ui.bottom-bar>
    @endif
    <x-ui.common.minigame></x-ui.common.minigame>
    @endif

    @if (!$forbidden)
    @if (!(isset($isIframe) && $isIframe && DetectDeviceHelper::isMobile()))

    <div id="footer-placeholder" class="mt-auto">
        <!--- hide bottom spin loading --->
        <!--- <div class="js-footer-loading min-h-[150px]">
                    <div class="inset-0 flex items-center justify-center z-1 w-[100vw]">
                        <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
                    </div>
                </div> --->
        <div id="footer-content">
            <x-footer />
        </div>
    </div>

            <x-ui.support></x-ui.support>
        @endif
        <go-jackpot pos="{{ $position }}" class="absolute z-[53]"></go-jackpot>
        <script type="module" src="{{ asset('js/float-go-icon.js?v=1.0.0') }}"></script>
        <script>
            window.notiModal = `<x-ui.noti-modal :id="'notification-modal'">
                            <x-ui.popup.notify></x-ui.popup.notify>
                        </x-ui.noti-modal>`;

            window.labelNew = `<x-kit.label type="new" direction="vertical" size="custom"></x-kit.label>`;
            window.labelEvent = `<x-kit.label type="event" direction="vertical" size="custom"></x-kit.label>`;
            window.labelHot = `<x-kit.label type="hot" direction="vertical" size="custom"></x-kit.label>`;
            window.labelLive = `<x-kit.label type="live" direction="vertical" size="custom"></x-kit.label>`;
    </script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"
        integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script type="text/javascript"
        src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"
        crossorigin="anonymous"></script>
    <script type="text/javascript"
        src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validation-unobtrusive/3.2.12/jquery.validate.unobtrusive.min.js"
        crossorigin="anonymous"></script>
    <script type="module" defer src="https://cdn.socket.io/4.8.1/socket.io.esm.min.js"></script>
    <script src="{{ asset('js/nanoplayer.4.min.js') }}"></script>
    @if (env('GTM_ID'))
    <!-- Google Tag Manager -->
    <script>
        (function(w,d,s,l,i){
            w[l]=w[l]||[];
            w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});
            var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),
                dl=l!='dataLayer'?'&l='+l:'';
            j.async=true;
            j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;
            f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','{{ env('GTM_ID') }}');
    </script> <!-- End Google Tag Manager -->

    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MM3C872D" height="0" width="0"
            style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    @endif
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" defer></script>

    <script>
        window.addEventListener("load", () => {
                window.__lc = window.__lc || {};
                window.__lc.license = {{ env('LIVE_CHAT_LICENSE') ?? env('LIVE_CHAT_LISENCE') }};
                window.__lc.integration_name = "manual_onboarding";
                window.__lc.product_name = "livechat";
                ;(function(n,t,c){function i(n){return e._h?e._h.apply(null,n):e._q.push(n)}var e={_q:[],_h:null,_v:"2.0",on:function(){i(["on",c.call(arguments)])},once:function(){i(["once",c.call(arguments)])},off:function(){i(["off",c.call(arguments)])},get:function(){if(!e._h)throw new Error("[LiveChatWidget] You can't use getters before load.");return i(["get",c.call(arguments)])},call:function(){i(["call",c.call(arguments)])},init:function(){var n=t.createElement("script");n.async=!0,n.type="text/javascript",n.src="https://cdn.livechatinc.com/tracking.js",t.head.appendChild(n)}};!n.__lc.asyncInit&&e.init(),n.LiveChatWidget=n.LiveChatWidget||e}(window,document,[].slice))
                if (isMobile()) {
                    window.LiveChatWidget.call('hidden');
                } else {
                    window.LiveChatWidget.call('minimize');
                }
            });
    </script>
    <noscript><a href="https://www.livechat.com/chat-with/{{ env('LIVE_CHAT_LICENSE') ?? env('LIVE_CHAT_LISENCE') }}/" r el="nofollow">Chat with
            us</a>, powered by <a href="https://www.livechat.com/?welcome" rel="noope
            ner nofollow" target="_blank">LiveChat</a></noscript>

    <script>
        window.openLiveChat = (event = null) => {
                event?.preventDefault();
                $('#chat-widget-container').css('display', 'block');
                window.LiveChatWidget.call('maximize')
                if ($('#auth-modal').children().length > 0 || $('#generic-modal').children().length > 0) {
                    $('#chat-widget-container').addClass('has-modal')
                } else {
                    $('#chat-widget-container').removeClass('has-modal')
                }
            }
    </script>

    <script>
        window.validRewardEvent = @json(request()->get('validRewardEvent'));
        window.validTopRacingEvent = @json(request()->get('validTopRacingEvent'));
        window.startTopRacingEvent = @json(request()->get('startTopRacingEvent'));
        window.startRewardEvent = @json(request()->get('startRewardEvent'));
        window.startFinalClubWorldCupEvent = @json(request()->get('startFinalClubWorldCupEvent'));
        window.validFinalClubWorldCupEvent = @json(request()->get('validFinalClubWorldCupEvent'));
        window.listTime = @json(request()->get('listTime'));
    </script>
    <script src="{{$GOOGLE_RECAPTCHA_SCRIPT}}"></script>
    @endif
    @stack('scripts')

    @if (Auth::check())
        <script src="{{ Module::asset('rewardgoldenhour:js/app.js') }}"></script>
    @endif
</body>

</html>