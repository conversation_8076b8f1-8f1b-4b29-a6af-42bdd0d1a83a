<div class="about-us-content font-normal text-sm text-neutral-1000 xl:space-y-6 space-y-4">
    <!-- Main Introduction -->
    <div class="intro space-y-4">
        <p class="text-base leading-relaxed">
            {{ __('about-us.intro_paragraph_1', ['brandName' => config('app.brand_name')]) }}
        </p>
        <p class="text-base leading-relaxed">
            {{ __('about-us.intro_paragraph_2') }}
        </p>
    </div>

    <!-- Headquarters -->
    <div class="headquarters bg-gray-50 p-4 rounded-lg border-l-4 border-yellow-400">
        <div class="flex items-start">
            <i class="fas fa-map-marker-alt text-yellow-500 mt-1 mr-3"></i>
            <p class="text-sm text-gray-700">
                {{ __('about-us.headquarters') }}
            </p>
        </div>
    </div>

    <!-- Motto -->
    <div class="motto bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
        <div class="flex items-start">
            <i class="fas fa-quote-left text-blue-500 mt-1 mr-3"></i>
            <p class="text-sm text-gray-700 italic">
                {{ __('about-us.motto') }}
            </p>
        </div>
    </div>

    <!-- Product Range Section -->
    <section class="product-range space-y-4">
        <h2 class="text-lg font-semibold text-gray-800 border-b-2 border-yellow-400 pb-2 flex items-center">
            <i class="fas fa-gamepad text-yellow-500 mr-2"></i>
            {{ __('about-us.product_range_title') }}
        </h2>
        <div class="space-y-3">
            <div class="bg-green-50 p-4 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-futbol text-green-500 mt-1 mr-3"></i>
                    <p class="text-sm text-gray-700">
                        {{ __('about-us.product_range_paragraph_1') }}
                    </p>
                </div>
            </div>
            <div class="bg-purple-50 p-4 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-dice text-purple-500 mt-1 mr-3"></i>
                    <p class="text-sm text-gray-700">
                        {{ __('about-us.product_range_paragraph_2') }}
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Reputation Section -->
    <section class="reputation space-y-4">
        <h2 class="text-lg font-semibold text-gray-800 border-b-2 border-yellow-400 pb-2 flex items-center">
            <i class="fas fa-award text-yellow-500 mr-2"></i>
            {{ __('about-us.reputation_title') }}
        </h2>
        <div class="space-y-3">
            <div class="bg-orange-50 p-4 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-handshake text-orange-500 mt-1 mr-3"></i>
                    <p class="text-sm text-gray-700">
                        {{ __('about-us.reputation_paragraph_1', ['brandName' => config('app.brand_name')]) }}
                    </p>
                </div>
            </div>
            <div class="bg-teal-50 p-4 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-users text-teal-500 mt-1 mr-3"></i>
                    <p class="text-sm text-gray-700">
                        {{ __('about-us.reputation_paragraph_2') }}
                    </p>
                </div>
            </div>
            <div class="bg-indigo-50 p-4 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-cogs text-indigo-500 mt-1 mr-3"></i>
                    <p class="text-sm text-gray-700">
                        {{ __('about-us.reputation_paragraph_3') }}
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Security Section -->
    <section class="security space-y-4">
        <h2 class="text-lg font-semibold text-gray-800 border-b-2 border-yellow-400 pb-2 flex items-center">
            <i class="fas fa-shield-alt text-yellow-500 mr-2"></i>
            {{ __('about-us.security_title') }}
        </h2>
        <div class="space-y-3">
            <div class="bg-red-50 p-4 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-globe text-red-500 mt-1 mr-3"></i>
                    <p class="text-sm text-gray-700">
                        {{ __('about-us.security_paragraph_1', ['brandName' => config('app.brand_name')]) }}
                    </p>
                </div>
            </div>
            <div class="bg-emerald-50 p-4 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-lock text-emerald-500 mt-1 mr-3"></i>
                    <p class="text-sm text-gray-700">
                        {{ __('about-us.security_paragraph_2') }}
                    </p>
                </div>
            </div>
            <div class="bg-cyan-50 p-4 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-shield-check text-cyan-500 mt-1 mr-3"></i>
                    <p class="text-sm text-gray-700">
                        {{ __('about-us.security_paragraph_3') }}
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Payment Methods Section -->
    <section class="payment space-y-4">
        <h2 class="text-lg font-semibold text-gray-800 border-b-2 border-yellow-400 pb-2 flex items-center">
            <i class="fas fa-credit-card text-yellow-500 mr-2"></i>
            {{ __('about-us.payment_title') }}
        </h2>
        <div class="space-y-3">
            <div class="bg-blue-50 p-4 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-university text-blue-500 mt-1 mr-3"></i>
                    <p class="text-sm text-gray-700">
                        {{ __('about-us.payment_paragraph_1') }}
                    </p>
                </div>
            </div>
            <div class="bg-green-50 p-4 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                    <p class="text-sm text-gray-700">
                        {{ __('about-us.payment_paragraph_2') }}
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Ethics Section -->
    <section class="ethics space-y-4">
        <h2 class="text-lg font-semibold text-gray-800 border-b-2 border-yellow-400 pb-2 flex items-center">
            <i class="fas fa-balance-scale text-yellow-500 mr-2"></i>
            {{ __('about-us.ethics_title') }}
        </h2>
        <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
            <div class="flex items-start">
                <i class="fas fa-heart text-yellow-600 mt-1 mr-3"></i>
                <p class="text-sm text-gray-700 font-medium">
                    {{ __('about-us.ethics_paragraph', ['brandName' => config('app.brand_name')]) }}
                </p>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <div class="cta bg-gradient-to-r from-yellow-400 to-yellow-600 p-6 rounded-lg text-center">
        <h3 class="text-lg font-bold text-gray-900 mb-2">
            {{ __('common.join_us_today') }}
        </h3>
        <p class="text-sm text-gray-800 mb-4">
            {{ __('common.experience_difference', ['brandName' => config('app.brand_name')]) }}
        </p>
        <div class="flex flex-col sm:flex-row gap-3 justify-center">
            <a href="{{ route('register') }}" class="bg-gray-900 text-white px-6 py-2 rounded-lg font-semibold hover:bg-gray-800 transition-colors">
                {{ __('auth.register') }}
            </a>
            <a href="{{ route('login') }}" class="bg-white text-gray-900 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                {{ __('auth.login') }}
            </a>
        </div>
    </div>
</div>