@use(App\Enums\UrlPathEnum)
@php
    $getImageActive = fn($name, $active) => "/asset/images/bottom-bar/" . ($active ? "{$name}-active" : $name) . ".avif";
    $path = request()->getPathInfo();
    $currentRoute = Route::currentRouteName();
    $isBottomBar = !in_array($currentRoute, config('footer.staticPage'));
    $isLoggedIn = Auth::check();

    $pageActive = [
        'home' => $path === '/',
        'help' => $path === '/help',
        'deposit' => str_contains($path, '/account/deposit'),
        'withdraw' => str_contains($path, '/account/withdraw'),
        'account' => (Str::contains($path, '/account') 
                     && !Str::startsWith($path, ['/account/withdraw', '/account/deposit'])) 
                     || $currentRoute === 'en.information.index',
    ];

    $navItems = [
        'home' => ['label' => __('pages.navigation.home'), 'route' => '/', 'icon' => 'home'],
        'help' => ['label' => __('pages.navigation.support'), 'route' => 'javascript:openLiveChat()', 'icon' => 'help'],
        'deposit' => ['label' => __('pages.account.deposit'), 'route' => '/account/deposit', 'icon' => 'deposit'],
        'withdraw' => ['label' => __('pages.account.withdraw'), 'route' => '/account/withdraw', 'icon' => 'withdraw'],
        'account' => ['label' => __('pages.account.my_account'), 'route' => '/account', 'icon' => 'account'],
    ];

    $helpItems = [
        'liveChat' => ['label' => 'Live Chat', 'route' => 'javascript:openLiveChat()', 'icon' => 'liveChat'],
        'messenger' => ['label' => 'Messenger', 'route' => env('FACEBOOK_LINK'), 'icon' => 'messenger'],
        'telegram' => ['label' => 'Telegram', 'route' => env('TELEGRAM_LINK'), 'icon' => 'telegram'],
        'channel' => ['label' => 'Channel', 'route' => env('TELEGRAM_CHANNEL_LINK'), 'icon' => 'channel'],
    ]
@endphp

@if ($isBottomBar)
<div class="bottom-menu-container fixed xl:hidden bottom-0 z-10 justify-center items-end h-[60px] w-full bg-neutral border-t-[1px] border-solid border-secondary-400 px-2">
    <div class="footer-item grid grid-cols-5 relative">
        @foreach ($navItems as $key => $item)
            @if ($key == 'help')
                <button
                    onclick="openHelp()"
                    class="js-help-bottom-menu style-menu-item relative">
                    <img 
                        class="js-tip w-[27px] h-[8px] absolute -top-[1px]"
                        alt="icon active" 
                        src="{{ $getImageActive('tip', true) }}"
                    />
                    <img 
                        class="active-menu-icon w-[24px] aspect-square hidden"
                        alt="icon" 
                        src="{{ $getImageActive($item['icon'], true) }}"
                    />
                    <img 
                        class="menu-icon w-[24px] aspect-square"
                        alt="icon" 
                        src="{{ $getImageActive($item['icon'], false) }}"
                    />
                    <span class="menu-label text-[10px] font-medium">{{ $item['label'] }}</span>
                </button>               
            @else
                <a 
                    href="{{ ($isLoggedIn || $key === 'home') ? $item['route'] : 'javascript:openLogin()' }}" 
                    class="js-menu-item style-menu-item relative {{$pageActive[$key]? "active" : ''}}">
                    <img 
                        class="js-tip w-[27px] h-[8px] absolute -top-[1px]"
                        alt="icon active" 
                        src="{{ $getImageActive('tip', true) }}"
                    />
                    <img 
                        class="active-menu-icon w-[24px] aspect-square hidden"
                        alt="icon" 
                        src="{{ $getImageActive($item['icon'], true) }}"
                    />
                    <img 
                        class="menu-icon w-[24px] aspect-square"
                        alt="icon" 
                        src="{{ $getImageActive($item['icon'], false) }}"
                    />
                    <span class="menu-label text-[10px] font-medium">{{ $item['label'] }}</span>
                </a>
            @endif

        @endforeach

        <div 
            class="js-help-container help-menu-container hidden absolute -top-[102px] w-[calc(100%-4px)] ml-[2px] bg-help-menu rounded-[12px] p-[10px]" 
            style="backdrop-filter: blur(30px);box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.1);">
            <div class="h-[62px] w-full grid grid-cols-4 gap-[8px]">
                @foreach ($helpItems as $key => $item)
                    <a href="{{$item['route']}}" class="help-item {{$key}}" onclick="openHelp()"
                        @if ($key !='liveChat')
                            target="_blank"
                        @endif
                    >
                        <img src="/asset/images/bottom-bar/help/{{$item['icon']}}.svg" alt="{{$key}}" class="w-[24px] aspect-square">
                        <span class="leading-[18px] text-xs font-medium text-neutral">{{$item['label']}}</span>
                    </a>
                @endforeach
            </div>
        </div>

    </div>
</div>
@pushOnce('scripts')
    <script>
        const currentActiveMenu = $('.js-menu-item.active');

        const openHelp = ()=>{
            currentActiveMenu.toggleClass('active');
            $('.js-help-bottom-menu').toggleClass('active');
            $('.js-help-container').toggleClass('hidden');
        }
        
        $(document).on('click', (event) => {
        if (!$(event.target).closest('.js-help-container, .js-help-bottom-menu').length) {
            currentActiveMenu.addClass('active');
            $('.js-help-container').addClass('hidden');
            $('.js-help-bottom-menu').removeClass('active');
        }
    });
    </script>
@endPushOnce
@endif
