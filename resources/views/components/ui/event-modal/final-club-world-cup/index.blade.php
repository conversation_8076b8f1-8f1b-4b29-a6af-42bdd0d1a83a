@props(['id' => 'final-club-world-cup-info', 'modalWrapperClass' => '', 'modalClass' => ''])
@php
    use App\Enums\GatewayEndpoint;
    $brandName = config('app.brand_name');
@endphp

<div id="{{$id}}" class="fixed inset-0 z-[54] flex justify-center items-center bg-black bg-opacity-70 border-box overflow-auto overflow-x-hidden scrollbar p-0 min-[440px]:p-12px {{ $modalWrapperClass }}">
    <div 
        id="{{$id}}-container"
        class="flex justify-center items-center w-full max-w-[658px] mb-0 mt-auto min-[440px]:mb-auto max-[440px]:!rounded-b-none bottom-modal-wrapper"
    >
        <div class="relative max-w-[658px] mt-[25px] xl:mt-[36px] bg-neutral-50 rounded-2xl px-[16px] xl:px-[24px] pt-[36px] xl:pt-[60px] pb-[24px] max-[440px]:!rounded-b-none">

            <button class="absolute top-3 right-3 size-8 xl:hover:opacity-80" onclick="closeInfoModal()">
                <img 
                    class="w-full aspect-square"
                    src="{{ asset('/asset/images/event/final-club-world-cup/close-gray.avif') }}"
                />
            </button>

            <div class="absolute top-[-25px] xl:top-[-36px] left-1/2 translate-x-[-50%] h-[50px] w-[212px] xl:h-[73px] xl:w-[309px]">
                <img 
                    class="h-full w-auto hidden xl:block"
                    src="{{ asset('/asset/images/event/final-club-world-cup/modal-title.avif') }}"
                />

                <img 
                    class="h-full w-auto xl:hidden"
                    src="{{ asset('/asset/images/event/final-club-world-cup/modal-title-mb.avif') }}"
                />

            </div>

            <div class="max-h-[500px] xl:max-h-max overflow-y-auto">
                <p class="font-semibold text-sm text-neutral-1000 leading-6">THỂ LỆ</p>
                <div class="mt-1 bg-neutral-150 rounded-lg px-[10px] py-[16px] mb-[16px]">
                    <ul class="list-disc ml-[16px] text-neutral-1000">
                        <li class="text-sm leading-5">Áp dụng cho trận chung kết giải 
                            <span class="font-bold">Fifa Club World Cup</span> 
                            lúc 02:00 AM ngày 14/07/2025 giờ Việt Nam.
                        </li>
                        <li class="text-sm leading-5 mt-2">Hoàn tiền cho vé cược thể thao đầu tiên nếu cược cửa 
                            <span class="font-bold"> TÀI </span> 
                            với kèo 
                            <span class="font-bold mt-2"> TÀI XỈU TOÀN TRẬN</span>, có kết quả là 
                            <span class="font-bold mt-2"> THUA </span> 
                            hoặc 
                            <span class="font-bold"> THUA MỘT NỬA</span>.
                        </li>
                        <li class="text-sm leading-5 mt-2">{{ __('pages.events.cashback_50_percent') }}</li>
                        <li class="text-sm leading-5 mt-2">Tiền bảo hiểm được trao trong vòng 24h sau khi vé cược có kết quả hợp lệ.</li>
                    </ul>
                </div>
    
                <p class="font-semibold text-sm text-neutral-1000 leading-[18px]">ĐIỀU KHOẢN</p>
    
                <div class="mt-1 bg-neutral-150 rounded-lg px-[10px] py-[16px]">
                    <ul class="list-disc ml-[16px] text-neutral-1000">
                        <li class="text-sm leading-5">{{ __('pages.events.applies_to_sports', ['brandName' => $brandName]) }}</li>
                        <li class="text-sm leading-5 mt-2">Không áp dụng chung với gói khuyến mãi nạp lần đầu.</li>
                        <li class="text-sm leading-5 mt-2">Các vé cân kèo, có kết quả hoà, bị huỷ cược và có tỷ lệ cược dưới  1.5 (DEC), -200 (US), -2 (INDO), 0.5 (MY), 0.5 (HK) sẽ không được tính vào số vé hợp lệ.</li>
                        <li class="text-sm leading-5 mt-2"><span class="font-bold">{{$brandName}}</span> có quyền chấm dứt, tạm dừng hoặc hủy bỏ chương trình khuyến mãi này bất kì lúc nào mà không cần thông báo trước.</li>
                        <li class="text-sm leading-5 mt-2">{{ __('pages.events.general_terms_apply', ['brandName' => $brandName]) }}</li>
                        <li class="text-sm leading-5 mt-2">Tiền bảo hiểm có thể rút ngay không cần hoàn thành vòng cược.</li>
                    </ul>
                </div>
            </div>

            <div class="flex justify-center mt-[24px]">
                <button 
                    onclick="openSport({
                        link: '/ca-do-bong-da/ksports',
                        apiUrl: '{{ GatewayEndpoint::K_SPORTS }}'
                    })"
                    class="w-[181px] h-[44px] xl:hover:opacity-80">
                        <img 
                        class="w-full h-full"
                        src="{{ asset('/asset/images/event/final-club-world-cup/bet-btn.avif') }}"
                        />
                </button>
            </div>
        </div>
    </div>
</div>

@pushOnce('scripts')
    <script>
        function closeInfoModal () {
            closeModal();
            window.handleOpenListPopup()
        }
    </script>
@endPushOnce