@php
use App\Enums\UrlPathEnum;
$totalJackpot = sum_jackpot();
@endphp

<div @class(['w-full flex justify-center px-[10px]', $class])>
    <div class="w-full max-w-desktop">
        <x-ui.title-section title="{{ __('pages.games.games_title_1') }}" titleButton="" titleHighlight="{{ __('pages.games.games_title_2') }}" class="mb-2 xl:mb-[23px]"></x-ui.title-section>
        <div class="block xl:hidden">
        <div class="mb-[8px]">
                <a href="/cong-game/no-hu" class="relative block">
                    <img src="{{ asset('asset/images/home/<USER>/banner-mb.avif') }}" alt="banner" class="w-full aspect-[292/89] rounded-lg" />
                    <div class="absolute bottom-[12px] left-0 flex flex-col w-full">
                        <div class="w-full translate-y-[10px]"> 
                            <img src="{{ asset('asset/images/home/<USER>/bg-jackpot.png') }}" alt="jackpot" class="w-full max-w-[177px]"/>
                        </div>
                        <div class="w-max pl-[11px] absolute bottom-0 left-0">
                            <div class="flex justify-center items-center gap-[4px] w-full px-[8px] min-w-[150px] h-[26px]">
                                <img src="{{ asset('asset/images/home/<USER>/coin.avif') }}" alt="coin" class="size-[11px]"/>
                                <p class="js-total-jackpot font-open min-w-[120px] jackpot-money text-[13.5px] leading-[20px] font-bold text-primary-200">0</p>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            <div class="grid grid-cols-3 gap-2">
                <a href="{{ UrlPathEnum::CASINO }}" class="rounded-lg block overflow-hidden">
                    <img src="{{ asset('asset/images/home/<USER>/live-casino-mb.avif') }}" alt="livecasino" class="w-full aspect-[92/102]" loading="lazy">
                </a>
                <a href="{{ UrlPathEnum::GAME_CARD }}" class="rounded-lg block overflow-hidden">
                    <img src="{{ asset('asset/images/home/<USER>/game-bai-mb.avif') }}" alt="{{ __('pages.casino.card_games') }}" class="w-full aspect-[92/102]" loading="lazy">
                </a>
                <a href="/lode" class="rounded-lg block overflow-hidden">
                    <img src="{{ asset('asset/images/home/<USER>/lode-mb.avif') }}" alt="{{ __('pages.lottery.lottery_title') }}" class="w-full aspect-[92/102]" loading="lazy">
                </a>
                <a href="{{ UrlPathEnum::SPORTS }}" class="rounded-lg block overflow-hidden">
                    <img src="{{ asset('asset/images/home/<USER>/thethao-mb.avif') }}" alt="{{ __('pages.sports.sports_title') }}" class="w-full aspect-[92/102]" loading="lazy">
                </a>
                <a href="/quayso" class="rounded-lg block overflow-hidden">
                    <img src="{{ asset('asset/images/home/<USER>/quayso-mb.avif') }}" alt="{{ __('pages.games.number_games') }}" class="w-full aspect-[92/102]" loading="lazy">
                </a>
                <button onclick="onClickMenuItem('{{ UrlPathEnum::E_SPORTS }}')" class="rounded-lg block overflow-hidden">
                    <img src="{{ asset('asset/images/home/<USER>/esports-mb.avif') }}" alt="e-sports" class="w-full aspect-[92/102]" loading="lazy">
                </button>
            </div>
        </div>
        <div class="hidden gap-3 xl:grid xl:grid-cols-[355fr_873fr] overflow-hidden">
            <a href="/cong-game/no-hu" class="relative">
                <img src="{{ asset('asset/images/home/<USER>/banner.avif') }}" alt="banner" class="max-w-[355px] aspect-[355/512] rounded-xl" loading="lazy" />
                <div class="absolute bottom-[31px] left-0 flex flex-col w-full">
                    <div class="w-full pl-[9px] translate-y-5">
                        <img src="{{ asset('asset/images/home/<USER>/bg-jackpot.avif') }}" alt="jackpot" class="w-full aspect-[428/152]" loading="lazy"/>
                    </div>
                    <div class="w-full px-[30px] absolute -bottom-1.5 left-0">
                        <div class="flex justify-start items-center gap-[7px] w-full pr-1 pl-2">
                            <img src="{{ asset('asset/images/home/<USER>/coin.avif') }}" alt="coin" class="size-[24px]"/>
                            <p class="js-total-jackpot font-open min-w-60 jackpot-money text-[28px] leading-[42px] font-bold text-primary-200 mx-auto">{{ number_format($totalJackpot, 0, '.', ',') }}</p>
                        </div>
                    </div>
                </div>
            </a>
            <div class="flex flex-col gap-3 flex-grow">
                <div class="grid grid-cols-[224px_224px_401px] gap-3">
                    <a href="{{ UrlPathEnum::CASINO }}" class="rounded-xl block overflow-hidden">
                        <img src="{{ asset('asset/images/home/<USER>/live-casino.avif') }}" alt="livecasino" class="w-full aspect-[224/250] duration-300 transition-all hover:scale-105" loading="lazy">
                    </a>
                    <a href="{{ UrlPathEnum::GAME_CARD }}" class="rounded-xl block overflow-hidden">
                        <img src="{{ asset('asset/images/home/<USER>/game-bai.avif') }}" alt="{{ __('pages.casino.card_games') }}" class="w-full aspect-[224/250] duration-300 transition-all hover:scale-105" loading="lazy">
                    </a>
                    <a href="/cong-game/game-khac" class="rounded-xl block overflow-hidden">
                        <img src="{{ asset('asset/images/home/<USER>/lode.avif') }}" alt="{{ __('pages.lottery.lottery_title') }}" class="w-full aspect-[401/250] duration-300 transition-all hover:scale-105" loading="lazy">
                    </a>
                </div>
                <div class=" grid grid-cols-[401px_224px_224px] gap-3">
                    <a href="{{ UrlPathEnum::SPORTS }}" class="rounded-xl block overflow-hidden">
                        <img src="{{ asset('asset/images/home/<USER>/thethao.avif') }}" alt="{{ __('pages.sports.sports_title') }}" class="w-full aspect-[401/250] duration-300 transition-all hover:scale-105" loading="lazy">
                    </a>
                    <a href="/cong-game/quay-so-number-games" class="rounded-xl block overflow-hidden">
                        <img src="{{ asset('asset/images/home/<USER>/quayso.avif') }}" alt="{{ __('pages.games.number_games') }}" class="w-full aspect-[224/250] duration-300 transition-all hover:scale-105" loading="lazy">
                    </a>
                    <button onclick="onClickMenuItem('{{ UrlPathEnum::E_SPORTS }}')" class="rounded-xl block overflow-hidden">
                        <img src="{{ asset('asset/images/home/<USER>/esports.avif') }}" alt="e-sports" class="w-full aspect-[224/250] duration-300 transition-all hover:scale-105" loading="lazy">
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    async function getSportUrl(endpoint) {
        try {
            const response = await fetch(`/api/v1${endpoint}`);
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error getting sport URL:', error);
            return null;
        }
    }

    async function onClickMenuItem(item) {
        const cookies = getCookies();
        if (!cookies || !cookies.user) {
            openLogin();
            return;
        }
        const user = JSON.parse(cookies.user || "{}");

        if (user.is_updated_fullname == 0) {
            openChangeName();
            return;
        }

        try {
            const response = await getSportUrl(item);
            if (response.data && response.data.url) {
                window.open(response.data.url, '_blank');
            }
        } catch (error) {
            console.error('Error parsing menu item:', error);
        }
    }
</script>
@endpush
