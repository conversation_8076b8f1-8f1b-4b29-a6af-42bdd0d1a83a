@if ((isset ($gameList) && $gameList && count($gameList) > 0) || (isset ($newGameList) && $newGameList && count($newGameList) > 0))
    @pushOnce('preloadLink')
        @foreach ($gameList as $game)
            <link rel="preload" href="{{ asset($game->image ?? '') }}" as="image" type="image/webp">
            <link rel="preload" href="{{ asset($game->image_mobile ?? '') }}" as="image" type="image/webp">
        @endforeach
    @endPushOnce
    @php
        $swiperConfig= [
            'slidesPerView' => 2.45,
            'spaceBetween' => 6,
            'breakpoints' => [
                '1200' => [
                    'slidesPerView' => 3.1812,
                    'spaceBetween' => 12,
                ],
            ],
            'speed' => 800,
            'loop' => true,
            'autoplay' => [
                'delay' => 3500,
                'disableOnInteraction' => false,
            ],
        ];
        $swiperClassPC = 'livestreamlist__content-hot-swiper';
        $swiperClassPCNew = 'livestreamlist__content-new-swiper';
        if (!isset($type)) {
            $type = 'home';
        }

        $showJackpotPromoList = [
            [
                "id" => "bc_77784",
                "url" => asset('/asset/images/home/<USER>/jackpot-promo-13.avif'),
                "class" => "w-[90px]"
            ],
            [
                "id" => "bacca_77778",
                "url" => asset('/asset/images/home/<USER>/jackpot-promo-13.avif'),
                "class" => "w-[90px]"
            ],
            [
                "id" => "xd_77786",
                "url" => asset('/asset/images/home/<USER>/jackpot-promo-100.avif'),
                "class" => "w-[96px]"
            ],
            [
                "id" => "sb_77783",
                "url" => asset('/asset/images/home/<USER>/jackpot-promo-999.avif'),
                "class" => "w-[99px]"
            ],
        ];

        $promoURL = null;
        $promoClass = '';
        $highlightGame = null;
        $games = [];
        $jackpot = 0;

        if (count($gameList) > 0) {
            foreach ($gameList as $index => $gameItem) {
                if ( $index == 0 ) {
                    $highlightGame = $gameItem;
                } else {
                    array_push($games, $gameItem);
                }
            }

            if (!$highlightGame) {
                $highlightGame = $gameList[0] ?? null;
                $games = array_slice($gameList, 1, 6) ?? [];
            }
        }

        $partnerGameJacpot = collect([
            'rik_vgmn_108',
            'rik_vgmn_109',
            'rik_vgmn_110',
            'rik_vgmn_111',
            'go_qs_txgo-101',
            'go_qs_xocdia-102',
            'go_vgmn_109',
            'go_vgmn_100',
            'go_vgmn_221',
            'b52_vgmn_108',
            'b52_vgmn_109',
            'b52_vgmn_110',
            '789club_G1X_305',
            '789club_G1X_306',
            'sunwin_G1S_305',
            'sunwin_G1X_306',
            'sun_g1s_305',
            'sun_g1s_306',
        ]);

        if ($highlightGame) {
            foreach ($showJackpotPromoList as $item) {
                if ($item['id'] === $highlightGame?->partner_game_id) {
                    $promoURL = $item['url'];
                    $promoClass = $item['class'];
                    break;
                }
            }

            $jackpot = get_jackpot_by_game_id( $partnerGameJacpot->contains(($highlightGame?->partner ?? '') . '_' . $highlightGame?->partner_game_id) ? (($highlightGame?->partner ?? '') . '_' . $highlightGame?->partner_game_id) : $highlightGame?->partner_game_id);
        }
    @endphp
    @pushOnce('preloadLink')
        <link rel="preload" as="image" href="{{ asset('asset/images/home/<USER>/img-preview-highlight.webp') }}" type="image/webp">
    @endPushOnce
    <div class="xl:py-[60px] {{ isset($type) && $type === 'lobby' ? 'pb-[12px]' : 'pb-[16px]' }}">
        @if (!isset($type) || $type !== 'lobby')
            <div class="flex justify-between items-center relative container">
                <h2 class="font-semibold leading-[26px] mr-auto text-[18px] text-neutral-950 capitalize xl:leading-[43px] xl:text-[36px]">
                    {{ __('pages.casino.live_casino_title_1') }}
                    <span class="text-secondary-400">{{ __('pages.casino.live_casino_title_2') }}</span>
                </h2>
                <a class="cursor-pointer font-medium xl:hover:text-neutral-1000 leading-[18px] ml-auto text-[12px] text-neutral-800 xl:leading-[20px] xl:text-[14px] flex items-center gap-[4px] xl:gap-[6px]"
                        href="/song-bai-livecasino-truc-tuyen">
                        {{ __('common.see_more') }}
                        <img src="{{ asset('asset/icons/arrow-right.svg') }}" alt="see more" class="size-[14px] xl:size-[20px]">
                </a>
            </div>
        @endif
        <div class="flex flex-col xl:flex-row xl:gap-5 gap-[8px] mt-2 xl:mt-5 xl:max-h-[372px] container !pr-0 xl:!pr-[0.625rem]">
            <div class="live-highlight-container relative xl:w-[52.6613%] w-full xl:pr-0 {{ !isset($type) || $type !== 'lobby' ? 'pr-[0.625rem]' : '' }}">
                <div class="js-keep-video-custom absolute z-[6] xl:top-4 xl:left-4 left-2 top-2 flex items-center gap-2">
                    <div
                        class="js-game-favorite relative text-neutral !pointer-events-auto cursor-pointer h-5 w-5 text-[12px] bg-black-90 rounded-full flex items-center justify-center xl:h-[30px] xl:w-[30px]"
                        data-game-id="{{ $highlightGame?->partner_game_id }}"
                        data-name="{{ $highlightGame->name ?? '' }}"
                        data-type="casino"
                        data-provider="{{ $highlightGame->partner ?? '' }}"
                    >
                        <i class="pointer-events-none {{ ($highlightGame && isset($highlightGame->is_favorite) && $highlightGame->is_favorite) ? 'icon-favorited' : 'icon-unfavorite' }} ml-[1px] text-[12px] [&.icon-favorited]:text-secondary-500 [&.icon-unfavorite]:text-neutral xl:text-[18px]"></i>
                        <img class="js-favorite-loading absolute w-[12px] aspect-square top-1/2 left-1/2 translate-x-[-50%] translate-y-[-50%] opacity-0 pointer-events-none xl:w-[18px]" src="{{ asset('asset/icons/spinner.svg') }}" alt="loading">
                    </div>
                    <div
                        class="js-toggle-sound text-neutral !pointer-events-auto cursor-pointer h-5 w-5 text-[12px] bg-black-90 rounded-full flex items-center justify-center xl:h-[30px] xl:w-[30px]"
                        data-playerid="playerid_{{ $highlightGame->partner }}_{{ $highlightGame?->partner_game_id }}"
                    >
                        <i class="icon-unvolum flex justify-center items-center text-[12px] xl:text-[18px]"></i>
                    </div>
                </div>
                <div class="js-live-game-item-preview-player  overflow-hidden">
                    <div
                        id="playerid_{{ $highlightGame->partner }}_{{ $highlightGame?->partner_game_id }}"
                        data-gameid="{{ $highlightGame->partner }}_{{ $highlightGame?->partner_game_id }}"
                        class="js-game-card-item-live-stream live-game-item-preview !size-auto aspect-[292/167] xl:aspect-[653/367] cursor-pointer relative col-span-1 w-full js-live-game-item-preview xl:rounded-lg overflow-hidden rounded-[6px]"
                        data-game="{{ json_encode($highlightGame) }}"
                        data-id="{{ $highlightGame->partner }}_{{ $highlightGame?->partner_game_id }}"
                        data-type="casino">
                        <div class="js-keep-video-custom absolute z-[2] xl:top-4 xl:right-4 right-2 top-2 flex flex-col items-end gap-[6px]">
                            <div class="flex items-center gap-2 h-5 px-2 py-[1px] bg-black-50 rounded-full xl:h-[22px]">
                                <div class="flex items-center gap-1">
                                    <div class="js-live-dot-playerid_{{ $highlightGame->partner }}_{{ $highlightGame?->partner_game_id }} ring-container relative size-[6px]">
                                        <div class="circle w-full h-full bg-alert-error rounded-full"></div>
                                        <span class="ringring absolute block top-0 left-0 w-full h-full rounded-full opacity-0"></span>
                                        <span class="ringring absolute block top-0 left-0 w-full h-full rounded-full opacity-0"></span>
                                        <span class="ringring absolute block top-0 left-0 w-full h-full rounded-full opacity-0"></span>
                                    </div>
                                    <p class="text-[12px] leading-[18px] font-medium text-neutral xl:font-semibold">LIVE</p>
                                </div>
                                <div class="js-live-game-item-preview-viewers hidden items-center gap-2">
                                    <span class="w-[1px] h-4 bg-black-800"></span>
                                    <div class="flex items-center gap-1">
                                        <i class="icon-viewer text-[14px] text-neutral"></i>
                                        <p 
                                            data-game-id="{{ $highlightGame?->partner_game_id }}" 
                                            data-partner="{{ $highlightGame?->partner }}" 
                                            class="js-item-viewers js-{{$highlightGame?->partner}}-viewers !min-w-[20px] text-[12px] leading-[18px] font-medium text-neutral xl:font-normal"
                                        >
                                            {{ $highlightGame->viewers ?? 0 }}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            @if (isset($highlightGame?->partner))
                                <div class="js-keep-video-custom hidden h-[37px] text-center xl:block">
                                    <img src="{{ asset('asset/icons/home/<USER>/' . strtolower($highlightGame->partner) . '.avif') }}"
                                        alt="{{ $highlightGame->partner }}"
                                        class="object-cover js-live-game-provider h-full" loading="lazy"
                                    />
                                </div>
                            @endif
                        </div>
                        <div class="js-keep-video-custom js-mb-img-video-custom img-video-custom block xl:hidden rounded-[6px] absolute top-0 left-0 w-full h-full z-[0]">
                            <img src="{{ asset('asset/images/home/<USER>/img-preview-' . $highlightGame?->partner_game_id . '.avif') }}"
                                onerror="this.onerror=null; this.src='{{ asset('asset/images/home/<USER>/img-preview-highlight.avif') }}'"
                                class="w-full js-live-game-item-preview-thumbnail object-cover aspect-[292/167] xl:aspect-[653/367]"
                                alt="{{ $highlightGame->name ?? '' }}" />
                        </div>
                        <div class="js-keep-video-custom js-pc-img-video-custom img-video-custom hidden w-full xl:block absolute top-0 left-0 z-[0]">
                            <img src="{{ asset('asset/images/home/<USER>/img-preview-' . $highlightGame?->partner_game_id . '.avif') }}"
                                class="w-full h-full js-live-game-item-preview-thumbnail aspect-[292/167] xl:aspect-[653/367] object-cover"
                                onerror="this.onerror=null; this.src='{{ asset('asset/images/home/<USER>/img-preview-highlight.avif') }}'"
                                alt="{{ $highlightGame->name ?? '' }}"  />
                        </div>
                        <div class="js-keep-video-custom absolute z-[2] bottom-0 left-0 px-2 py-2 xl:py-4 xl:px-4 w-full flex justify-between items-center xl:items-end">
                            <div class="flex flex-col text-sm text-neutral xl:gap-1">
                                <div class="w-full js-live-game-item-preview-name text-[14px] leading-[20px] font-medium capitalize xl:text-[16px] xl:leading-[24px] xl:font-normal">{{ $highlightGame->name ?? '' }}</div>
                                <div class="w-full gap-0.5 items-center px-2 rounded-full xl:h-6 h-[18px] bg-black-50 js-jackpot-value-{{ $highlightGame?->partner }}_{{ $highlightGame?->partner_game_id }} {{ $jackpot && $jackpot > 0 ? 'flex' : 'hidden' }}"
                                    data-game-id="{{ $highlightGame?->partner }}_{{ $highlightGame?->partner_game_id }}" data-partner="{{ $highlightGame?->partner }}">
                                    <img src="{{ asset('asset/images/home/<USER>') }}" class="w-[14px] min-w-[14px] aspect-square"
                                        loading="lazy" alt="ic-jackpot" />
                                    <span class="text-gold-550 font-medium live-game-item-jackpot xl:text-sm text-[10px] leading-[1.4] xl:leading-[1.5]">{{ number_format($jackpot, 0, '.', ',') }}</span>
                                </div>
                                @if ($promoURL)
                                    <img src="{{ $promoURL }}" class="js-jackpot-promo h-[14px] object-fill {{ $promoClass }} {{ $jackpot && $jackpot > 0 ? 'hidden' : 'block' }}"/>
                                @endif
                            </div>
                            <div class="js-live-game-item-preview-player-hover">
                                <x-kit.button
                                    data-game="{{ json_encode($highlightGame) }}"
                                    style="out-line: none;"
                                    class="js-livestream-play live-stream-play hidden h-[28px] xl:h-[40px] min-w-[84px] w-[84px] whitespace-nowrap capitalize xl:w-[106px] xl:min-w-[106px] rounded-full bg-neutral text-primary-700 border border-primary-700 xl:block xl:hover:border-primary-400 text-xs leading-[1.5] font-medium xl:text-sm xl:leading-[20px]">
                                    <p class="translate-y-[-1px] whitespace-nowrap capitalize">{{ __('common.play_now') }}</p>
                                </x-kit.button>
                                @if (isset($highlightGame->partner))
                                    <div class="js-keep-video-custom block h-5 text-center xl:hidden">
                                        <img 
                                            src="{{ asset('asset/icons/home/<USER>/' . strtolower($highlightGame->partner) . '.avif') }}"
                                            alt="{{ $highlightGame->partner }}"
                                            class="object-cover js-live-game-provider h-full" loading="lazy"
                                        />
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="xl:w-[calc(100%_-_(52.6613%_+_20px))] w-full">
               <div class="highlights-game flex-col gap-1 hidden xl:flex">
                    <div class="highlights-game__title font-medium leading-[1.2] text-[36px] text-neutral-1200">
                        {{ $highlightGame->name ?? '' }}
                    </div>
                    <div class="flex flex-col gap-2">
                        <div class="highlights-game__viewers text-sm leading-[1.2] text-neutral-1800">
                            <span class="font-semibold js-item-viewers js-{{$highlightGame?->partner}}-viewers" data-game-id="{{ $highlightGame?->partner_game_id }}" data-partner="{{ $highlightGame?->partner }}">{{ $highlightGame?->viewers ?? 0 }}</span> {{ __('pages.casino.playing_now') }}
                        </div>
                    </div>
                </div>
                <div class="livestreamlist flex flex-col gap-2 xl:gap-6 xl:mt-6">
                    <div class="livestreamlist__tab flex gap-2">
                        <div
                            class="js-livestreamlist-tab-item livestreamlist__tab-item cursor-pointer active rounded-full pl-2 pr-1 xl:pl-4 xl:pr-[0.625rem] flex items-center justify-between text-xs xl:text-base font-medium text-neutral-1650 w-[78px] h-[32px] xl:h-[44px] xl:w-[112px]"
                            data-tab="live-hot"
                        >
                            {{ __('pages.games.games') }} <span class="livestreamlist__tab-item-count text-sm hidden min-w-[22px] xl:min-w-6 xl:w-6 xl:h-6 w-[22px] h-[22px] rounded-full items-center justify-center bg-count-items ml-[4px] xl:ml-[8px]">{{ count($gameList) - 1 }}</span>
                        </div>
                        <div
                            class="js-livestreamlist-tab-item livestreamlist__tab-item text-nowrap cursor-pointer rounded-full pl-2 pr-1 xl:pl-4 xl:pr-[0.625rem] flex items-center justify-center text-xs xl:text-base font-medium text-neutral-1650 min-w-[78px] h-[32px] xl:h-[44px] xl:min-w-[112px]"
                            data-tab="live-new"
                        >
                            {{ __('pages.games.new_games') }} <span class="livestreamlist__tab-item-count text-sm hidden min-w-[22px] xl:min-w-6 xl:w-6 xl:h-6 w-[22px] h-[22px] rounded-full items-center justify-center bg-count-items ml-[4px] xl:ml-[8px]">6</span>
                        </div>
                    </div>
                    <div class="livestreamlist__content">
                        <div id="live-hot" class="livestreamlist__content-hot">
                            <x-kit.swiper :swiperRequiredClass="$swiperClassPC" class="overflow-x-auto" id="hotlivestream-swiper" :swiperConfig="$swiperConfig">
                                @foreach ($games as $item)
                                    <div class="swiper-slide mr-[8px] max-w-[40%] xl:max-w-[170px] xl:mr-[12px] [&_.title]:!text-[13.1px] [&_.partner-text]:!text-[9.83px] [&_.js-game-favorite]:!text-[13.1px]">
                                        <x-ui.card
                                            name="{{ $item->name ?? 'title' }}"
                                            image="{{ $item->image ?? '' }}"
                                            :game="$item"
                                            page="home"
                                            type="casino"
                                            data-api="{{ $item->api_url ?? '' }}"
                                            id="{{ $item?->partner_game_id ?? '' }}"
                                            provider="{{ $item->partner_txt ?? '' }}"
                                            table_id="{{ $item->table_id ?? '' }}"
                                            partner="{{ $item->partner ?? '' }}"
                                            favorite="{{ isset($item->is_favorite) && $item->is_favorite ? 'favorite' : '' }}"
                                            class="flex flex-col items-center text-marron loader-image-transparent"
                                            tags="{{ $item->tags ?? '' }}"
                                            isSlider
                                            isLiveStream
                                            showWinRate
                                        >
                                        </x-ui.card>
                                    </div>
                                @endforeach
                            </x-kit.swiper>
                        </div>

                        <div id="live-new" class="livestreamlist__content-new opacity-0 visible h-0">
                            <x-kit.swiper :swiperRequiredClass="$swiperClassPCNew" class="overflow-x-auto" id="newlivestream-swiper" :swiperConfig="$swiperConfig">
                                @foreach (($newGameList ?? []) as $newGame)
                                    <div class="swiper-slide mr-[8px] max-w-[40%] xl:max-w-[170px] xl:mr-[12px] [&_.title]:!text-[13.1px] [&_.partner-text]:!text-[9.83px] [&_.js-game-favorite]:!text-[13.1px]">
                                        <x-ui.card
                                            name="{{ $newGame->name ?? 'title' }}"
                                            image="{{ $newGame->image ?? '' }}"
                                            :game="$newGame"
                                            page="home"
                                            type="casino"
                                            data-api="{{ $newGame->api_url ?? '' }}"
                                            id="{{ $newGame->partner_game_id ?? '' }}"
                                            provider="{{ $newGame->partner_txt ?? '' }}"
                                            partner="{{ $newGame->partner ?? '' }}"     
                                            favorite="{{ isset($newGame->is_favorite) && $newGame->is_favorite ? 'favorite' : '' }}"
                                            class="flex flex-col items-center text-marron loader-image-transparent"
                                            tags="{{ $newGame->tags ?? '' }}"
                                            isSlider
                                            isLiveStream
                                            showWinRate
                                        >
                                        </x-ui.card>
                                    </div>
                                @endforeach
                            </x-kit.swiper>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @pushOnce('scripts')
        <script>
            $('.js-livestreamlist-tab-item').on('click', function(event) {
                event.preventDefault();
                $('.js-livestreamlist-tab-item').removeClass('active');
                $(this).addClass('active');
                const tab = $(this).data('tab');
                $(`.livestreamlist__content-hot, .livestreamlist__content-new`).addClass('opacity-0 visible h-0');
                $(`#${tab}`).removeClass('opacity-0 visible h-0');
            });
        </script>
        @vite('resources/js/home/<USER>')
    @endPushOnce
@endif
