@props([
'title' => '',
'titleHighlight' => '',
'titleButton' => '',
'link' => '',
'class' => null,
'swiperConfig' => null,
'swiperRequiredClass' => null,
'list' => [],
])

@php
$topGamesSection = translate_text_with_config(config('home.topGamesSection'));
@endphp

<div @class(['w-full flex justify-center px-[10px]', $class])>
    <div class="w-full max-w-desktop">
        <div class="flex justify-between items-center mb-2 xl:mb-5">
            <x-ui.title-section title="{{ $title }}" titleButton="{{ $titleButton }}" titleHighlight="{{ $titleHighlight }}" link="{{ $link }}"></x-ui.title-section>
            <div class="xl:flex gap-2 hidden">
                <button class="topgames-promotion-nav text-primary-600 w-8 h-8 text-[16px] flex justify-center items-center topgames-prev bg-neutral-100 rounded-full [&.swiper-button-disabled]:bg-neutral-50 [&.swiper-button-disabled>i::before]:text-neutral-300">
                    <i class="icon-arrow-left-fill"></i>
                </button>
                <button class="topgames-promotion-nav text-primary-600 w-8 h-8 text-[16px] flex justify-center items-center topgames-next bg-neutral-100 rounded-full [&.swiper-button-disabled]:bg-neutral-50 [&.swiper-button-disabled>i::before]:text-neutral-300">
                    <i class="icon-arrow-right-fill"></i>
                </button>
            </div>
        </div>
        <div>
            <div class="mr-[-10px] xl:mr-0">
                <x-kit.swiper :swiperConfig="$swiperConfig" swiperRequiredClass="lottery-swiper" swiperWrapperClass="card-group" swiperRequiredClass="card-group-lottery">
                    @foreach ($list as $item)
                        <div class="swiper-slide max-w-[40%] xl:max-w-[190px] mr-[6px] xl:mr-[20px]">
                            <x-ui.card
                                name="{{ $item->name ?? 'title' }}"
                                image="{{ $item->image ?? '' }}"
                                :game="$item"
                                type="game"
                                data-api="{{ $item->api_url ?? '' }}"
                                id="{{ $item->partner_game_id ?? '' }}"
                                provider="{{ $item->partner_txt ?? '' }}"
                                partner="{{ $item->partner ?? '' }}"
                                page="home"
                                favorite="{{ isset($item->is_favorite) && $item->is_favorite ? 'favorite' : '' }}"
                                class="flex flex-col items-center text-marron loader-image-transparent"
                                tags="{{ $item->tags ?? '' }}"
                            >
                            </x-ui.card>
                        </div>
                    @endforeach
                </x-kit.swiper>
            </div>
        </div>
    </div>
</div>
