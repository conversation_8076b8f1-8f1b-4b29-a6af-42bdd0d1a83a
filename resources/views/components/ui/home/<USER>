<?php
 $promotionList = config('events.promotionList');
$swiperConfig = config('home.promotionSwiperConfig') ?? [];
?>

<div class="xl:mb-[40px] xl:container">
    <div class="flex justify-between items-center relative px-[10px] xl:px-0">
        <p class="font-semibold leading-[26px] mr-auto text-[18px] text-neutral-950 capitalize xl:leading-[43px] xl:text-[36px]">
            {{ __('pages.promotions.promotions_title_1') }}
            <span class="text-secondary-400">{{ __('pages.promotions.promotions_title_2') }}</span>
        </p>
        <a class="cursor-pointer font-medium xl:hover:text-neutral-1000 leading-[18px] ml-auto text-[12px] text-neutral-800 xl:leading-[20px] xl:text-[14px] flex items-center gap-[4px] xl:gap-[6px]"
                href="/events?tab=promotions">
                {{ __('common.see_more') }}
                <img src="{{ asset('asset/icons/arrow-right.svg') }}" alt="see more" class="size-[14px] xl:size-[20px]">
            </a>
    </div>
    <div class="max-w-full overflow-hidden xl:pt-[23px] pt-2 xl:pb-0 pb-[80px] pl-[10px] xl:pl-0">
        <x-kit.swiper :swiperConfig="$swiperConfig" swiperWrapperClass="!w-[283px] xl:!w-[404px] relative max-[374px]:!w-[250px]" swiperRequiredClass="promotion-swiper"
            id="promotion">
            <!-- Slides -->
            @foreach ($promotionList as $promotion)
                <x-ui.promotion-card :$promotion ></x-ui.promotion-card>
            @endforeach
        </x-kit.swiper>
    </div>
</div>
