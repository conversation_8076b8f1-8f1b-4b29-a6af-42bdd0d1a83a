@props(['listNews'])
@php
    use App\Enums\UrlPathEnum;
    $imageBaseUrl = config('services.gw.image_base_url');
@endphp
<div>
    <div class="flex justify-between items-center relative px-[10px] xl:px-0 mb-[20px]">
        <p class="font-semibold leading-[26px] mr-auto text-[18px] text-neutral-950 capitalize xl:leading-[43px] xl:text-[36px]">{{ __('pages.home.news_title') }}</p>
        @if (count($listNews) > 0)
            <a class="cursor-pointer font-medium xl:hover:text-neutral-1000 leading-[18px] ml-auto text-[12px] text-neutral-800 xl:leading-[20px] xl:text-[14px] flex items-center gap-[4px] xl:gap-[6px]"
                href="/tin-tuc">
                {{ __('common.see_more') }}
                <img src="{{ asset('asset/icons/arrow-right.svg') }}" alt="see more" class="size-[14px] xl:size-[20px]">
            </a>
        @endif
    </div>
    <div class="max-w-full">
        @if (count($listNews) > 0)
            <div class="grid grid-cols-1 gap-[16px]">
                @foreach($listNews as $news)
                    <x-ui.news.card-news 
                        :data="$news"
                        direction="horizontal"
                        :isHiddenDescription="true"
                        class="w-full !grid-cols-[183px_auto] [&_.card-title]:text-neutral-1000 [&:nth-child(2)]:border-neutral-1750 [&:nth-child(2)]:border-t [&:nth-child(2)]:pt-[16px] [&_.card-image]:rounded-[14px] [&_.card-image>img]:aspect-[183/110] [&_.card-info]:bg-transparent rounded-none"
                    ></x-ui.news.card-news>
                @endforeach
            </div>
        @else
            <div class="flex flex-col justify-center items-center gap-4 w-full pt-[58px]">
                <img src="{{ asset('asset/images/home/<USER>/no-data.avif') }}" class="size-[100px]" alt="{{ __('pages.home.no_data') }}"/>
                <p class="text-sm leading-5 text-center text-neutral-800">{{ __('pages.home.no_data') }}</p>
            </div>
        @endif
    </div>
</div>
