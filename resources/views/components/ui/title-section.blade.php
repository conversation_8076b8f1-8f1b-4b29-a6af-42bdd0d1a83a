@props([
	'title' => 'title',
    'titleHighlight' => '',
    'titleButton' => 'titleButton',
    'link' => '1',
	'class' => null,
	'translateRevertTitle' => false,
])

<div @class(['flex justify-between items-center', $class])>
    @if (!empty($title))
        <h2 class="font-semibold flex items-center leading-[26px] mr-auto text-[18px] text-neutral-950 capitalize gap-2 xl:leading-[43px] xl:text-[36px] {{ $translateRevertTitle && App::isLocale('en') ? 'flex-row-reverse' : '' }}">{{ $title }} @if(!empty($titleHighlight))<span class="text-secondary-400">{{ $titleHighlight }}</span>@endif</h2>
    @endif

    @if (!empty($titleButton) && !empty($link))
    <div class="flex items-center gap-[4px] xl:gap-[6px]">
        <x-kit.link-button style="light" :href="$link" class="ml-auto font-normal">{{ $titleButton }}</x-kit.link-button>
        <img src="{{ asset('asset/icons/arrow-right.svg') }}" alt="see more" class="size-[14px] xl:size-[20px]">
    </div>
    @endif
</div>
