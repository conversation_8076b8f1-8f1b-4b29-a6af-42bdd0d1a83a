@props(['types','pageType' => 'game', 'activeFilter', 'swiperConfig'])

@php
    $itemLength = $pageType === "casino" ? 10 : 11; 
    $currentType = (request()->route('slug') =="favorite"? "favorite" :  $activeFilter['type']) ?? 'all';
    $activeIndex = 0;
    foreach ($types as $index => $type) {
        if ((!$currentType && $type->key === 'all') || $currentType === $type->key) {
            $activeIndex = $index ;
            break;
        }
    }

    $swiperConfig = [
        'slidesPerView' => $itemLength,
        'spaceBetween' => 8,
        'loop' => false,
        'initialSlide' => $activeIndex,
    ];
@endphp

<div class="game-type-filter relative w-full" id="{{ request()->path() === 'song-bai-livecasino-truc-tuyen' ?  'types-filter' : 'types-filter-game' }}">
    {{-- Desktop view with swiper --}}
    <div class="game-filter-container"
        role="group" {{ $attributes }}>

        <div class="hidden xl:block w-full">
            <x-kit.swiper :swiperConfig="$swiperConfig" swiperSection="xl:overflow-x-auto no-scrollbar" swiperRequiredClass="games-filter-sample-swiper" isOverflowVisible>
                @foreach ($types as $type)
                @php
                    $isActive = (!$currentType && $type->key === 'all') ||  $currentType === $type->key;
                @endphp
                <div class="swiper-slide h-[64px] mr-[8px] {{$pageType == 'casino' ? 'max-w-[116.8px]' : 'max-w-[105.45px]'}}">
                    <a href="{{ $type->link ?? '#' }}" class="game-filter-item relative w-full {{$isActive ? "slide-active" :""}}">
                        <img 
                            src="{{ asset("asset/icons/games/types/pc/icon-$type->key.avif") }}"
                            alt="{{ $type->name }}" 
                            class="h-[28px] aspect-square"
                        />
                        <span class="game-label">{{ ucfirst(mb_strtolower(string: $type->name)) }}</span>
    
                        @if(in_array($type->key, ['sexy', 'lottery']))
                        <img 
                            src={{ asset('asset/icons/games/types/icon-hot.svg') }}
                            alt="{{ $type->name }}"
                            class="w-[13px] h-[18px] absolute right-[6.45px] top-[5px]"
                        />
                        @endif
                    </a>
                </div>
                @endforeach
            </x-kit.swiper>
        </div>
        
        {{-- mobile swiper--}}
        <div class="flex xl:hidden items-center gap-6 no-scrollbar px-1 overflow-auto w-full">
            <div class="filter-swiper-mb flex items-center overflow-x-auto hide-scroll">
                @foreach ($types as $type)
                @php
                    $isActive = (!$currentType && $type->key === 'all') ||  $currentType === $type->key;
                @endphp
                <div class="swiper-slide !w-fit pr-2 h-[1.75rem]" data-type="{{$type->link}}">
                    <a href="{{ $type->link ?? '#' }}" class="game-filter-item-mb text-xs {{$isActive ? "active" :""}}">
                        <img 
                            src="{{ asset("asset/icons/games/types/icon-".$type->key.($isActive?"-active":"").".svg") }}"
                            alt="{{ $type->name }}"  
                            class="h-[20px] aspect-square"
                        />
                        <span class="game-label">{{ ucfirst(mb_strtolower($type->name)) }}</span>
                    </a>
                </div>
                @endforeach
            </div>
        </div>
        {{-- Mobile toggle button --}}
        <button class="style-toggle-button toggle-mobile-menu">
            <i class="icon-list-down toggle-icon"></i>
        </button>
    </div>

    {{-- Mobile accordion --}}
    <div class="game-filter-accordion mobile-menu" style="display: none;">
        <div class="accordion-box">
            <div class="pt-2.5 px-2.5 pb-[12px]">
                <span class="text-xs font-medium text-neutral-1000">
                    {{__('common.selected_game_title')}}
                </span>
            </div>
            <div class="px-2.5 max-[389px]:px-[4px] grid grid-cols-2 gap-[8px] max-h-[208px] overflow-y-auto overflow-x-hidden">
                @foreach ($types as $type)
                @php
                    $isActive = (!$currentType && $type->key === 'all') ||  $currentType === $type->key;
                @endphp
                <a href="{{ $type->link ?? '#' }}" class="accordion-item {{$isActive ? "active" :""}}">
                    <img 
                        src="{{ asset("asset/icons/games/types/icon-".$type->key.($isActive?"-active":"").".svg") }}"
                        alt="{{ $type->name }}"  
                        class="h-[20px] aspect-square"
                    />
                    <span class="game-label">{{ ucfirst(mb_strtolower($type->name)) }}</span>
                </a>
                @endforeach
            </div>
        </div>
    </div>
</div>

@pushOnce('scripts')
@vite(['resources/js/types-filter.js'])
@endpushOnce