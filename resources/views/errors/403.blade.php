@php
    $content = config('not-found');
@endphp

<x-layout class="[&>header]:hidden [&>footer]:hidden" forbidden>
    <div
        class="pt-[15px] xl:mt-0 mx-auto flex h-screen w-screen max-w-[1180px] flex-col items-center justify-center gap-6 lg:gap-[156px] px-[35px] lg:flex-row"
    >
        <img
            src="{{ asset('asset/images/errors/403/img-access-denied.avif') }}"
            alt="access Restricted"
            class="h-[200px] w-[280px] min-w-[280px] lg:h-[300px] lg:w-[420px] lg:min-w-[420px]"
            width="420"
            height="300"
        />
        <div
            class="flex flex-col items-start justify-center gap-6 lg:w-[calc(100%-420px)]"
        >
            <div
                class="flex items-start justify-center gap-6 mb-2 lg:items-center"
            >
                <img
                    src="{{ asset('asset/images/errors/403/img-uk-flag.svg') }}"
                    alt="access Restricted"
                    class="h-[50px] w-[50px]"
                    width="50"
                    height="50"
                />
                <div class="flex flex-col items-start gap-2">
                    <h1
                        class="text-sm font-bold text-neutral-1000"
                    >
                        Access Restricted
                    </h1>
                    <p class="text-sm text-neutral-800">
                        Sorry, your region is not supported.
                    </p>
                </div>
            </div>

            <div
                class="flex items-start justify-center gap-6 lg:items-center"
            >
                <img
                    src="{{ asset('asset/images/errors/403/img-vn-flag.svg') }}"
                    alt="access Restricted"
                    class="h-[50px] w-[50px]"
                    width="50"
                    height="50"
                />
                <div class="flex flex-col items-start gap-2">
                    <h1
                        class="text-sm font-bold text-neutral-1000"
                    >
                        {{ __('errors.access_denied') }}
                    </h1>
                    <p class="text-sm text-neutral-800">
                        Xin lỗi, khu vực của bạn không nằm trong danh sách hỗ
                        trợ.
                    </p>
                </div>
            </div>

            <hr class="w-full border-t border-[#D3D9DB] lg:max-w-[447px]" />

            @if ((request()->ip()) || (isset(request()->rayId) && request()->rayId))
                <div
                    class="flex items-start justify-start mx-[11.5px] gap-3 text-sm text-neutral-800 w-full lg:ml-[75px]"
                >
                    @if (request()->ip())
                        <div
                            class="w-1/2 lg:w-auto lg:min-w-[161px]"
                        >
                            <span
                                class="text-nowrap font-bold"
                            >
                                IP:
                            </span>
                            <span>{{ request()->ip() }}</span>
                        </div>
                    @endif

                    @if ((isset(request()->rayId) && request()->rayId) || (request()->header('cf-ray')))
                        <div
                            class="w-1/2 text-nowrap lg:w-auto lg:min-w-[161px]"
                        >
                            <span class="font-bold">
                                RAY ID:
                            </span>
                            <span>{{ request()->rayId ?? request()->header('cf-ray') }}</span>
                        </div>
                    @endif
                </div>
            @endif
        </div>
    </div>
</x-layout>