<x-layout class="[&>header]:hidden [&>header]:xl:block">
    <div class="sticky top-0 z-10 h-12 bg-primary-700 flex justify-center items-center xl:hidden">
        <a href="{{ $backUrl }}" class="absolute top-3 left-[10px]">
            <img class="w-6" src="/asset/icons/ic-back.svg" alt="back">
        </a>
        <span class="text-neutral text-base font-medium capitalize">
            {{ $title['mobile'] }}
        </span>
    </div>
    <div class="relative z-0 flex flex-col pb-[39px] pt-3 min-h-[calc(100vh_-_48px)] xl:min-h-max xl:pt-4 xl:pb-[60px] bg-neutral-50">
        <x-ui.breadcrumb :list="$breadCrump" class="container !bg-transparent !pt-0 hidden xl:flex"></x-ui.breadcrumb>
        <x-ui.breadcrumb :list="$mobileBreadCrump" class="container !static !z-0 !bg-transparent !py-0  xl:hidden mb-2"></x-ui.breadcrumb>
        <div class="container !px-3 xl:px-[10px]">
            <div class="flex gap-4">
                <div class="w-[240px] hidden xl:block">
                    <x-ui.navigation-info :list="$navList" :activeId="'about-us'"></x-ui.navigation-info>
                </div>
                <div class="flex-1 !h-fit bg-neutral rounded-xl py-6 pt-[12px] xl:pt-6 px-[10px] xl:px-6 xl:space-y-3 space-y-2">
                    <h1 class="text-primary-700 font-medium text-base">{{ with_brand_name($content['title']) }}</h1>
                    <x-static.about />
                    {{-- <p class="font-normal text-sm text-neutral-1000">{{ with_brand_name($content['description']) }}</p>
                    <h2 class="text-neutral-1000 text-sm font-normal !pl-1 xl:px-0">{{with_brand_name($content['info1'])}}</h2>
                    <p class="font-normal text-sm text-neutral-1000">{{ with_brand_name($content['desc1']) }}</p>
                    <h2 class="text-neutral-1000 text-sm font-normal !pl-1 xl:px-0">{{with_brand_name($content['info2'])}}</h2>
                    <div>
                        <p class="font-normal text-sm text-neutral-1000">{{ with_brand_name($content['desc2']) }}</p>
                        <ul class="list-disc pl-[26px]">
                            @foreach ($content['items2'] as $item)
                                <li class="font-normal text-sm text-neutral-1000">{{$item}}</li>
                            @endforeach
                        </ul>
                    </div>
                    <h2 class="text-neutral-1000 text-sm font-normal !pl-1 xl:px-0">{{with_brand_name($content['info3'])}}</h2>
                    <p class="font-normal text-sm text-neutral-1000">{!! with_brand_name_links($content['desc3']) !!}</p> --}}
                </div>
            </div>
        </div>
    </div>
</x-layout>
