@php
    $heroBanner = translate_text_with_config(config('home.heroBanner'));
    $tabList = translate_text_with_config(config('events.tabList'));
@endphp
@section('canonical', url()->full())
<x-layout>
    <div class="event-page relative z-0 flex flex-col pb-[101px] xl:pb-[60px] xl:min-h-[calc(100vh_-_482px)]">
        <x-ui.home.hero-banner :swiperConfig="$heroBanner['swiperConfig']" :list="$heroBanner['list']" class="block w-full !pb-0 xl:hidden"></x-ui.home.hero-banner>
        <x-ui.breadcrumb  :list="[['name' => $title, 'url' => request()->getRequestUri()]]" class="container"></x-ui.breadcrumb>
        <div class="container flex gap-[10px]">
            <x-ui.navigation-mb class="!top-[86px]"></x-ui.navigation-mb>
            <div class="flex flex-col items-start w-full">
                <div class="grid grid-cols-3 gap-2 w-full mb-3 xl:grid-cols-[repeat(3,130px)] xl:w-max xl:mb-6">
                    @foreach($tabList as $tabItem)
                        @if($tabItem['value'] === $selectedTab)
                            <x-kit.button class="hidden w-full font-normal xl:flex" style="filled" type="primary" link="{{ $tabItem['link']}}">{{ $tabItem['label'] }}</x-kit.button>
                            <x-kit.button class="flex w-full px-[7px] font-normal xl:hidden" style="filled" type="primary" link="{{ $tabItem['link']}}" size="medium">{{ $tabItem['label'] }}</x-kit.button>

                        @else
                            <x-kit.button class="hidden w-full font-normal bg-neutral-50 xl:flex" style="filled" type="tertiary" link="{{ $tabItem['link']}}">{{ $tabItem['label'] }}</x-kit.button>
                            <x-kit.button class="flex w-full px-[7px] font-normal bg-neutral-50 xl:hidden" style="filled" type="tertiary" link="{{ $tabItem['link']}}" size="medium">{{ $tabItem['label'] }}</x-kit.button>

                        @endif
                    @endforeach
                </div>
                @if (count($list) > 0)
                    <div class="grid grid-cols-1 gap-3 w-full xl:grid-cols-2 xl:gap-5 xl:min-h-[576px]">
                        @foreach ($list as $item)
                            <x-ui.card-event :data="$item"></x-ui.card-event>
                        @endforeach
                    </div>
                @else
                    <div class="flex flex-col items-center gap-4 w-full pt-[71px] pb-[71px] px-4 xl:pt-[221px] xl:pb-[199px]">
                        <img src={{ asset('asset/images/events/events-icon.avif') }} alt="icon event" class="w-[68px] h-[68px] xl:w-[100px] xl:h-[100px]"/>
                        <div class="flex flex-col items-center text-xs xl:text-sm leading-[20px] text-neutral-800">
                            <p>Không có sự kiện</p>
                            <p>Chúng tôi sẽ cập nhật trong thời gian tới.</p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-layout>
