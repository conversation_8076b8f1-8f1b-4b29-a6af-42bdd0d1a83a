@php
    $brandName = strtolower(config('app.brand_name'));
    $heroBanner = config('home.heroBanner');
    $listEmptyLayout = [route('en.lode-list.index'),route('en.daga-list.index'),route('en.quayso-list.index')];
    $emptyLayout = in_array(request()->url(),$listEmptyLayout);

    if (!Auth::user() && request()->is('cong-game/favorite')) {
        header('Location: /cong-game');
        exit();
    }
@endphp

<x-layout>
    <div class="banner relative">
        <section class="xl:block hidden">
            <div
                class="container flex flex-col gap-2 absolute left-1/2 top-[32px] -translate-x-1/2">
                <div class="text-[48px] leading-[60px] uppercase text-neutral-1000 font-bold">{{ __('pages.games.games_slots') }}</div>
                <div class="text-[20px] font-light leading-6 text-neutral-1000">
                    {{ __('pages.help.explore_entertainment', ['brandName' => strtoupper($brandName)]) }}
                </div>
            </div>
            <img 
                src="{{ asset('asset/images/games/banner.avif') }}" 
                class="min-w-full min-h-[145px] object-cover" 
                alt="icon"
                alt="banner" />
        </section>
        <section class="xl:hidden block">
            <x-ui.home.hero-banner :swiperConfig="$heroBanner['swiperConfig']" :list="$heroBanner['list']" class="block w-full !pb-0 xl:hidden"></x-ui.home.hero-banner>
        </section>
    </div>
    <div class="container games">
        @if ($emptyLayout)
         <!-- empty layout -->

            <x-ui.breadcrumb :list="[['name' => $breadcrumb, 'url' => $routeUrl]]"></x-ui.breadcrumb>
            <x-ui.navigation-mb class="!top-[86px]"></x-ui.navigation-mb>
            <div class="overflow-hidden xl:overflow-visible pl-[10px] xl:pl-0 xl:pr-0">
                <!-- hide favorite icon - da ga lode quay so -->
                <x-ui.game-card-container :$games hasFavorite={{false}} ></x-ui.game-card-container>
            </div>

        @else
        <!-- games -->

            <x-ui.breadcrumb :list="[['name' => $breadCrumbText, 'url' => $routeUrl]]"></x-ui.breadcrumb>
            <x-ui.navigation-mb class="!top-[86px]"></x-ui.navigation-mb>
            <div class="overflow-hidden xl:overflow-visible pl-[10px] xl:pl-0 xl:pr-0">
                <x-ui.card-container :$games :$activeFilter :$routeUrl :$gamesTotal :$filters :$swiperConfig type="game" />
            </div>
            
        @endif
    </div>
</x-layout>
