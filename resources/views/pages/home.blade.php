@php
    $heroBanner = translate_text_with_config(config('home.heroBanner'));
    $gameSection = translate_text_with_config(config('home.gameSection'));
    $navigationSection = translate_text_with_config(config('home.navigationSection'));
    $topGamesSection = translate_text_with_config(config('home.topGamesSection'));
    $sportSwiperConfig = translate_text_with_config(config('home.sportSwiperConfig'));
@endphp

<x-layout>
    <div class="relative z-0 xl:bg-neutral-50">
        <x-ui.home.hero-banner :swiperConfig="$heroBanner['swiperConfig']" :list="$heroBanner['list']"></x-ui.home.hero-banner>
        @if (!empty($nearWin))
            <div class="xl:block hidden">
                <x-ui.home.topjackpot-section :nearWin="$nearWin" />
            </div>
        @endif
        <div class="pl-[10px] xl:p-0">
            <x-ui.navigation-mb></x-ui.navigation-mb>
            <div class="overflow-hidden">
                <x-ui.home.live :gameList="$data['streamGames']" :newGameList="$newStreamGames" />
                <div class="hidden xl:block pb-4 xl:mb-[40px]">
                    <x-ui.home.sports :sportSwiperConfig="$sportSwiperConfig" swiperRequiredClass="home-sports-swiper" :hotMatches="$hotMatches" />
                </div>
                <x-ui.home.lottery-section class="pb-4 xl:pb-[40px]" :title="$topGamesSection['title']" :titleHighlight="$topGamesSection['titleHighlight']"></x-ui.home.lottery-section>
                @if (is_array($data['nohuGames']) && count($data['nohuGames']) > 0)
                    <div id="js-pc-loading-home-section">
                        <div class="js-pc-home-content-loaded">
                            <x-ui.home.topgame-section class="pb-4 xl:pb-[40px]" :list="$data['nohuGames']" :swiperConfig="$topGamesSection['swiperConfig']"
                                :swiperRequiredClass="$topGamesSection['swiperRequiredClass']" :title="$topGamesSection['title']" :titleHighlight="$topGamesSection['titleHighlight']">
                            </x-ui.home.topgame-section>
                        </div>
                    </div>
                @endif
                <div class="xl:bg-neutral">
                    <x-ui.home.promotions />
                </div>
                <div class="max-xl:hidden bg-news-gradient py-[20px]">
                    <div class="container grid grid-cols-2 gap-6">
                        <x-ui.home.news :listNews="$listNews" />
                        <x-ui.home.instruct />
                    </div>
                </div>
            </div>
        </div>
    </div>
    @pushOnce('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const pcLoadingHomeSection = document.getElementById('js-pc-loading-home-section');
            const mbLoadingHomeSection = document.getElementById('js-mb-loading-home-section');
            if (pcLoadingHomeSection) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            observer.unobserve(entry.target);
                        }
                    });
                });

                observer.observe(pcLoadingHomeSection);
            }
            if (mbLoadingHomeSection) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            $('.js-mb-home-content-loaded').removeClass('hidden');
                            $('.js-mb-loading-home-placeholder').remove();
                            observer.unobserve(entry.target);
                        }
                    });
                });

                observer.observe(mbLoadingHomeSection)
            }
        });
    </script>
    @endPushOnce
</x-layout>
