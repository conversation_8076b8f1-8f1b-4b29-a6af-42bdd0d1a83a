<!-- type: bet/transaction -->

@props([
'type' => 'transaction',
'data' => null
])

@php
    $statusBet = translate_text_with_config(config('account.history.statusBet'));
    $statusTransaction = translate_text_with_config(config('account.history.statusTransaction'));
    $bankList = translate_text_with_config(config('account.history.bankList'));

    $handleSetColorBet = function ($status) {
        if ($status === 'WIN' || $status === 'WON') {
            return 'bg-success-100 [&_p]:text-success-900';
        } else {
            return 'bg-danger-100 [&_p]:text-danger-600';
        }
    };

    $handleSetColorTransaction = function ($status) {
        switch ($status) {
            case 'CANCEL':
                return '[&_span]:bg-danger-600 [&_p]:text-danger-600';
            case 'DRAFT':
            case 'PENDING':
            case 'APPROVED':
            case 'PROCESSING':
            case 'WAITING':
                return '[&_span]:bg-warning-600 [&_p]:text-warning-600';
            default:
                return '[&_span]:bg-success-600 [&_p]:text-success-600';
        }
    };

    $isShowStatusList = isset($data -> method) && $data->action === 'WITHDRAW' && $data -> method === 'phone_card' && $data -> status === 'FINISHED';

    $getItemImage = function ($data) {
        $image = '';
        if(isset($data->method)){
            //codepay
            if(($data->method === 'bank_account' || $data->method === 'nicepay') && isset($data->to_bank_code) ){
                if ($data->action === 'DEPOSIT' && $data -> type === 'PROMOTION') {
                    return $image;
                } else {
                    $image = '/vendor/accounts/images/account/banks-logo/' . strtolower($data->to_bank_code ?? '') . '.svg';
                    return $image;
                }
            }
            //card
            if(($data->method === 'phone_card') && isset($data->to_bank_code) ){
                $image = '/vendor/accounts/images/account/network/' . strtolower($data->to_bank_code ?? '') . '.svg';
                return $image;
            }

            //cryptopay
            if(($data->method === 'cryptopay') && isset($data->to_bank_code) ){
                $image = '/vendor/accounts/images/account/crypto-pay/' . strtolower($data->to_bank_code ?? '') . '.svg';
                return $image;
            }
        }

        return $image;
    };

    $bankName = $data -> to_bank_code ?? '';
    if (isset($data->to_bank_name) && $data->to_bank_name) {
        $bankName = $data->to_bank_name;
    } else if (isset($data->to_bank_code) && $data->to_bank_code && isset($bankList[$data->to_bank_code])) {
        $bankName = $bankList[$data->to_bank_code];
    }
@endphp
<div id="hcollapse-header-{{ $data -> id ?? '' }}" data-id="{{ $data -> id ?? '' }}" class="flex flex-col gap-[6px] w-full pb-[9px] pt-[10px] px-[12px] border-b-[1px] border-solid border-b-neutral-150">
    <div class=" relative flex justify-between items-center">
        <button data-value="{{ $data -> id ?? '' }}" class="copy-button border-none flex items-center gap-[2px] py-[2px] px-[8px] bg-neutral-100 text-neutral-950 rounded-[100px]">
            <img src="{{ asset('asset/icons/account/history/copy.svg') }}" alt="arrow-down" />
            <p class="text-[10px] leading-[calc(14/10)] font-medium text-neutral-950 pointer-events-none">{{ $data -> id }}</p>
        </button>
        <div class="flex gap-1">
            <p class="history-time text-[12px] leading-[calc(18/12)] text-neutral-950" data-time="{{ $data -> created_time }}"></p>
            <img src="{{ asset('asset/images/arrow-down.avif') }}" alt="arrow-down"
                class="w-[18px] h-[18px] object-contain transition-all duration-200"
                id="arrow-hcollapse-{{ $data -> id ?? '' }}">
        </div>
    </div>

    <div id="mini-information-{{$data -> id ?? ''}}" class="mini-information">
        <div class="flex justify-between items-center py-[1px] mb-[6px]">
            @if ($type === 'bet')
            <p class="text-[12px] leading-[calc(18/12)]] text-neutral-950 max-w-[15.625rem] line-clamp-1 truncate whitespace-normal">{{ $data -> product ?? '' }}</p>
            <x-accounts::ui.account.status-history :item="$data" />
            @else
            <div class="flex items-center gap-[4px]">
                @if(isset($data -> action) && $data -> action === 'DEPOSIT')
                    <p class="text-[12px] leading-[calc(18/12)] text-neutral-1000 capitalize">
                        {{ isset($data -> type) && $data -> type === 'PROMOTION' ? "Khuyến mãi" : "Nạp tiền" }}
                    </p>
                @else
                <p class="text-[12px] leading-[calc(18/12)] text-neutral-1000 capitalize">Rút tiền</p>
                @endif
            </div>
            <p class="text-[12px] leading-[calc(18/12)] font-semibold text-neutral-1000">{{ $data -> amount_txt ?? '' }} K</p>
            @endif
        </div>
        <div class="flex items-center justify-between gap-[4px] py-[1px]">
            @if ($type === 'bet')
                <p class="text-[12px] leading-[calc(18/12)] text-neutral-800">Thưởng</p>
                <p class="text-[12px] leading-[calc(16/12)] font-semibold"> {{ $data -> winlost  }} K</p>
            @else
                <div class="py-[2px] px-[6px] rounded-[100px] {{ isset($data->method_txt) ? 'bg-neutral-50' : 'bg-neutral' }}">
                    <p class="text-[10px] leading-[calc(14/10)] font-medium text-neutral-950">
                        {{ $data->method_txt ?? '' }}
                    </p>
                </div>
                <div data-id="{{ $data -> id}}" class="flex items-center gap-[4px] {{ $handleSetColorTransaction($data -> status) }}  {{ $isShowStatusList ? 'js-status-button status-button-mb' : '' }}">
                    <span class="w-[4px] h-[4px] rounded-full pointer-events-none"></span>
                    <p class="text-[12px] leading-[calc(18/12)] pointer-events-none {{ $isShowStatusList ? 'underline' : '' }}">{{ $statusTransaction[$data -> status] ?? '' }}</p>
                </div>
            @endif
        </div>
    </div>

    <div id="full-information-{{$data -> id ?? ''}}" class="full-information hidden">
        @if ($type === 'bet')
        <div class="flex items-center justify-between py-[1px] mb-[6px]">
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-800 capitalize">Game</p>
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-950 max-w-[15.625rem] line-clamp-1 truncate whitespace-normal">{{ $data -> product ?? '' }}</p>
        </div>
        <div class="flex items-center justify-between py-[1px] mb-[6px]">
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-800 capitalize">Số tiền</p>
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-950 font-semibold">{{ $data -> stake ?? 0 }} K</p>
        </div>
        <div class="flex items-center justify-between py-[1px] mb-[6px]">
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-800 capitalize">Thắng/Thua</p>
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-950 font-semibold">{{ $data -> winlost ?? 0 }} K</p>
        </div>
        <div class="flex items-center justify-between py-[1px] mb-[6px]">
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-800 capitalize">Turnover</p>
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-950 font-semibold">{{ $data -> turnover_rolling ?? 0 }} K</p>
        </div>
        <div class="flex items-center justify-between py-[1px]">
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-800 capitalize">Trạng thái</p>
            <x-accounts::ui.account.status-history :item="$data" />
        </div>
        @else
        <div class="flex items-center justify-between py-[1px] mb-[6px]">
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-800 capitalize">Loại</p>
            @if(isset($data -> action) && $data -> action === 'DEPOSIT')
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-950">
                {{ isset($data -> type) && $data -> type === 'PROMOTION' ? "Khuyến mãi" : "Nạp tiền" }}
            </p>
            @else
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-950">Rút Tiền</p>
            @endif
        </div>
        <div class="flex items-center justify-between py-[1px] mb-[6px]">
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-800 capitalize">Phương thức</p>
            @if (isset($data->method_txt))
            <p class="text-[10px] leading-[calc(14/10)] text-neutral-950 rounded-[100px] px-[6px] py-[2px] bg-neutral-100">{{ $data->method_txt ?? '' }}</p>
            @endif
        </div>
        <div class="flex items-center justify-between py-[1px] mb-[6px]">
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-800 capitalize">Ngân hàng</p>
            <div class="flex items-center gap-x-1">
                @if ($getItemImage($data))
                    <img 
                        src="{{ asset($getItemImage($data)) }}" 
                        alt="{{ $data->to_bank_code ?? "" }} icon" 
                        class="w-[16px] h-[16px]" 
                        onerror="this.src='{{ asset('vendor/accounts/images/account/banks-logo/bank-logo-default.svg') }}';"
                        />
                @endif
                @if (isset($data->method) && ($data->method === 'bank_account' || $data->method === 'nicepay') && isset($data->to_bank_name) && $data->to_bank_name)
                    <p class="text-[12px] leading-[calc(18/12)] text-neutral-1000">{{ $data->to_bank_name ?? ($bankName ?? '') }}</p>
                @else
                    <p class="text-[12px] leading-[calc(18/12)] text-neutral-1000">{{ $bankName }}</p>
                @endif
                @if (isset($data->live_check) && $data->live_check)
                    <a href="{{ isset($data->live_check) ? $data->live_check : '' }}" target="_blank"
                        class="cursor-pointer">
                        <img src="{{ asset('asset/images/account/history/cryptolink.svg') }}" alt="icon" class="w-[18px] h-[18px]">
                    </a>
                @endif
                
            </div>
        </div>
        <div class="flex items-center justify-between py-[1px] mb-[6px]">
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-800 capitalize">Số Tiền</p>
            <p class="text-[12px] leading-[calc(16/12)] text-neutral-1000 font-semibold">{{ $data->amount_txt ?? '' }} K</p>
        </div>
        <div class="flex items-center justify-between py-[1px]">
            <p class="text-[12px] leading-[calc(18/12)] text-neutral-800 capitalize">Trạng Thái</p>
            <div data-id="{{ $data -> id}}" class="flex items-center gap-[4px] {{ $handleSetColorTransaction($data -> status) }} {{ $isShowStatusList ? 'js-status-button status-button-mb' : ''}}">
                <span class="w-[4px] h-[4px] rounded-full pointer-events-none"></span>
                <p class="text-[12px] leading-[calc(18/12)] pointer-events-none {{ $isShowStatusList ? 'underline' : ''}}">{{ $statusTransaction[$data -> status] ?? '' }}</p>
            </div>
        </div>
        @endif
    </div>

    @if ($isShowStatusList)
        <x-accounts::ui.account.status-list :$data isMobile></x-accounts::ui.account.status-list>
    @endif
</div>

@pushOnce('scripts')
<script>
    document.addEventListener("DOMContentLoaded", () => {
        $('.copy-button').each(function(index, item) {
            $(this).on('click', (event) => {
                event.stopPropagation();
                let copyValue = '';
                const {
                    value
                } = event.target.dataset;

                copyValue = value;
                if (!copyValue) {
                    copyValue = $(event.target).find('p').text();
                }

                navigator.clipboard.writeText(copyValue);
                $(this).addClass('active');
                setTimeout(() => {
                    $(this).removeClass('active');
                }, 1000);
            })
        });
        $('[id^=hcollapse-header-]').on('click', function() {
            const id = $(this).data('id');
            $(`#hcollapse-header-${id}`).toggleClass('bg-neutral-250')
            $(`#full-information-${id}`).slideToggle(200);
            $(`#mini-information-${id}`).slideToggle(200);
            $(`#arrow-hcollapse-${id}`).toggleClass('rotate-180');
        });
    })
</script>
@endPushOnce
