@props([
    'data',
    'accountInfo',
    'todayBet' => 0,
    'todayTotalReturn' => 0,
    'totalReturn' => 0,
    'currentMultiplier' => 0,
    'currentPercent' => 0,
    'noPadding' => false,
    'isHiddenCancel' => false,
    'isShowTitle' => true,
    'commission',
    'swiperClass' => 'swiper-promotion',
    'casinoInfo',
    'slotInfo',
    'createdTime' => '',
    'endTime' => '',
    'isOverview' => false
])

@php
    $promotionList = translate_text_with_config(config('events.promotionList'));

    $validPromotion = [];

    if (isset($accountInfo->package_id)) {
        $validPromotion = array_filter($promotionList, function ($item) use ($accountInfo) {
            return $item['id'] !== 2;
        });
    }

    $promotionSwiperConfig = [
        'slidesPerView' => 1,
        'spaceBetween' => 0,
        'pagination' => [
            'el' => '.swiper-pagination',
            'clickable' => true,
        ],
    ];
@endphp

@if ($data['id'] !== 2)
    <div @class([
        'flex flex-col gap-4 w-full bg-neutral rounded-[24px]',
        'xl:py-[24px] xl:px-[20px]' => !$noPadding,
    ])>
        <div>
            <div class="flex justify-between items-center rounded-2xl w-full bg-neutral-50 px-5 py-[18px] xl:py-2 {{ $isOverview ? 'xl:h-[60px]' : '' }}">
                <p class="text-xs font-medium text-neutral-800 leading-[calc(18/12)]">
                    {{ __('account-overview.promotion.data.total_refund') }}
                </p>
                <p class="text-[16px] leading-[24px] xl:text-[24px] xl:leading-[36px] font-medium text-neutral-1000">
                    {{ $totalReturn < 1 ? $totalReturn : number_format($totalReturn ?? 0, 0, '.', ',') }} K</p>
            </div>
        </div>
        <div class="block [&_.swiper-pagination]:!relative [&_.swiper-pagination]:!top-0 [&_.swiper-section]:min-h-[104px] xl:[&_.swiper-section]:min-h-fit [&_.swiper-pagination]:h-2 [&_.swiper-pagination]:flex [&_.swiper-pagination]:xl:!flex [&_.swiper-pagination]:justify-center [&_.swiper-pagination.swiper-pagination-bullets]:!mt-[16px] 
        [&_.swiper-pagination-bullet]:!w-[8px] [&_.swiper-pagination-bullet]:!mx-[4px] [&_.swiper-pagination-bullet]:!h-[8px] [&_.swiper-pagination-bullet]:bg-neutral-100  [&_.swiper-pagination-bullet-active]:!bg-primary-500 {{ $isOverview ? '' : '[&_.swiper-pagination-bullet]:xl:!w-[44px] [&_.swiper-pagination-bullet]:xl:!mx-[2px] [&_.swiper-pagination]:xl:h-[6px] [&_.swiper-pagination-bullet]:xl:!h-[6px] [&_.swiper-pagination-bullet]:xl:!rounded-[20px] [&_.swiper-pagination-bullet]:xl:bg-primary-50' }}">
            <x-kit.swiper :swiperConfig="$promotionSwiperConfig" :swiperRequiredClass="$swiperClass">
                @foreach ($validPromotion as $promotion)
                    <div class="swiper-slide">
                        <div class="flex flex-col gap-1">
                            @php
                                $listBonus = [];
                                $percent = 0;

                                if ($promotion['id'] !== 1) {
                                    if ($promotion['id'] === 4) {
                                        $listBonus = $casinoInfo->addition_cashback_level ?? [];
                                        $percent = $casinoInfo->percentAdditionalBonus ?? 0;
                                    } elseif ($promotion['id'] === 3) {
                                        $listBonus = $slotInfo->week_cashback_level ?? [];
                                        $percent = $slotInfo->week_percent ?? 0;
                                    }
                                }
                            @endphp
                            <div class="promotion-header grid grid-cols-[100px_auto] gap-[12px] xl:grid-cols-[216px_auto] xl:gap-[16px]">
                                <a href="{{ $promotion['link'] }}">
                                    <picture>
                                        <source media="(min-width: 1200px)" srcset="{{ asset($promotion['image-thumb']) }}">
                                        <img src={{ asset($promotion['image-thumb-mb']) }}
                                            class="promotion-banner w-full max-w-[100px] aspect-[100/80] rounded-[12px] xl:max-w-[216px] xl:aspect-[216/144]"
                                            alt="promotion" />
                                    </picture>
                                </a>
                                <div class="flex flex-col justify-center">
                                    @if ($isShowTitle)
                                        <p
                                            class="hidden text-[10px] leading-[14px] font-bold text-primary-700 uppercase xl:block">
                                            {{ $promotion['type'] ?? '' }}</p>
                                    @endif
                                    <a
                                        href="{{ $promotion['link'] }}"
                                        class="text-[14px] leading-[20px] font-semibold text-neutral-1000 xl:text-[16px] xl:leading-[24px] capitalize"
                                    >
                                        {{ $promotion['sub-title-detail'] ?? '' }}
                                    </a>
                                    <p class="text-[12px] leading-[18px] xl:text-[14px] xl:leading-5 text-neutral-800">
                                        {{ $promotion['sub-description-detail'] ?? '' }}</p>

                                    @if (count($listBonus) > 0)
                                        <div class="promotion-bonus flex flex-col gap-[2px] mt-1">
                                            <div class="flex justify-between items-center">
                                                <p class="text-[10px] leading-[14px] font-medium text-neutral-1000">Phần
                                                    thưởng bổ sung</p>
                                            </div>
                                            <div class="w-full h-[6px] rounded-full bg-neutral-100 xl:h-[8px]">
                                                <div class="h-full rounded-full bg-gradient-to-r from-[#E6F5FF] to-[#B0E0FF] relative"
                                                    style="width: {{ $percent }}%">
                                                    <div
                                                        class="absolute rounded-full w-2 h-[6px] xl:h-2 bg-promotion-progress top-0 {{ $percent ? 'right-0' : 'left-0' }}">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex justify-between items-center">
                                                <div class="relative flex flex-col items-start">
                                                    <p
                                                        class="text-[10px] leading-[14px] font-medium text-neutral-800 xl:font-bold">
                                                        %</p>
                                                    <p
                                                        class="absolute left-0 top-full hidden text-[9px] leading-[14px] text-neutral-800 xl:block">
                                                        K</p>
                                                </div>
                                                @foreach ($listBonus as $index => $item)
                                                    <div
                                                        class="relative flex flex-col {{ $index === count($listBonus) - 1 ? 'items-end' : 'items-center' }}">
                                                        <p
                                                            class="text-[10px] leading-[14px] font-medium text-neutral-800 xl:font-bold">
                                                            {{ $item->percent }}%</p>
                                                        <p
                                                            class="absolute top-full hidden w-max text-[9px] leading-[14px] text-neutral-800 xl:text-neutral-600 xl:block {{ $index === count($listBonus) - 1 ? 'right-0' : 'left-1/2 translate-x-[-50%]' }}">
                                                            ({{ $item->min_stake_txt }} K)
                                                        </p>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            @if (count($listBonus) > 0)
                                <div class="promotion-bonus-overview hidden flex-col gap-[2px] xl:hidden">
                                    <div class="flex justify-between items-center">
                                        <p class="text-[10px] leading-[14px] font-medium text-neutral-1000">Phần
                                            thưởng bổ sung</p>
                                    </div>
                                    <div class="w-full h-[6px] rounded-full bg-neutral-100 xl:h-[8px]">
                                        <div class="h-full rounded-full bg-gradient-to-r from-[#E6F5FF] to-[#B0E0FF] relative"
                                            style="width: {{ $percent }}%">
                                            <div
                                                class="absolute rounded-full w-2 h-[6px] xl:h-2 bg-promotion-progress top-0 {{ $percent ? 'right-0' : 'left-0' }}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <div class="relative flex flex-col items-start">
                                            <p
                                                class="text-[10px] leading-[14px] font-medium text-neutral-800 xl:font-bold">
                                                %</p>
                                            <p
                                                class="absolute left-0 top-full hidden text-[9px] leading-[14px] text-neutral-800 xl:block">
                                                K</p>
                                        </div>
                                        @foreach ($listBonus as $index => $item)
                                            <div
                                                class="relative flex flex-col {{ $index === count($listBonus) - 1 ? 'items-end' : 'items-center' }}">
                                                <p
                                                    class="text-[10px] leading-[14px] font-medium text-neutral-800 xl:font-bold">
                                                    {{ $item->percent }}%</p>
                                                <p
                                                    class="absolute top-full hidden w-max text-[9px] leading-[14px] text-neutral-800 xl:text-neutral-600 xl:block {{ $index === count($listBonus) - 1 ? 'right-0' : 'left-1/2 translate-x-[-50%]' }}">
                                                    ({{ $item->min_stake_txt }} K)
                                                </p>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </x-kit.swiper>
        </div>
        <div class="promotion-detail grid grid-cols-1 gap-3 xl:grid-cols-[587fr_291fr] {{ $isOverview ? 'xl:gap-[16px]' : 'xl:gap-[18px]' }}">
            <div class="promotion-chart flex flex-col gap-4 items-center xl:items-start {{ $isOverview ? 'xl:gap-4' : 'xl:gap-3'}}">
                <div
                    class="promotion-chart-info grid grid-cols-7 w-full h-[104px] rounded-[8px] border border-neutral-100 {{$isOverview ? 'xl:!h-[118px]' : 'xl:h-[98px]' }}">
                    @foreach ($commission as $index => $item)
                        @php
                            $bet =
                                $item['bet'] < 1
                                    ? 0
                                    : number_format(str_replace(',', '', strval($item['bet'])) ?? 0, 0, '.', ',');
                            $return =
                                $item['return'] < 1
                                    ? 0
                                    : number_format(str_replace(',', '', strval($item['return'])) ?? 0, 0, '.', ',');
                        @endphp
                        <button @class([
                            'chart-item relative flex flex-col items-center gap-1 h-full p-2 border-r border-neutral-100 cursor-pointer last:border-none [&:hover_.date]:xl:text-secondary-700 [&:hover_.tooltip]:xl:flex xl:hover:bg-neutral-250',
                        ]) data-date="{{ $item['day'] }}"
                            data-bet="{{ $bet }}" data-refund="{{ $return }}">
                            <div class="flex flex-col justify-end flex-grow w-full max-w-[30px] pointer-events-none">
                                <div class="w-full bg-neutral-150" style="height: {{ $item['returnHeight'] }}%">
                                </div>
                                <div class="w-full bg-secondary-700" style="height: {{ $item['betHeight'] }}%;"></div>
                            </div>
                            <p class="date text-[12px] leading-[18px] text-neutral-800 pointer-events-none [.chart-item.active_&]:text-secondary-700">
                                {{ $item['day'] }}</p>

                            <div
                                class="tooltip absolute bottom-[93px] left-1/2 z-10 -translate-x-1/2 hidden flex-col justify-center items-start gap-1 min-w-[170px] h-[60px] py-[10px] px-3 rounded-[8px] bg-neutral-250 shadow-[0px_2px_4px_0px_#2221211A]">
                                <span
                                    class="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2 rotate-45 w-[10px] h-[10px] bg-neutral-250"></span>
                                <div class="flex flex-col gap-[2px]">
                                    <div class="grid grid-cols-[14px_66px_auto] items-center gap-1">
                                        <span class="w-[14px] h-[14px] bg-secondary-700 rounded-[4px]"></span>
                                        <div class="flex justify-between items-center w-full">
                                            <p class="text-[12px] leading-[18px] font-medium text-neutral-1000">
                                                Tổng cược</p>
                                            <p class="text-[12px] leading-[18px] text-neutral-1000">:</p>
                                        </div>
                                        <p class="text-[12px] leading-[18px] text-neutral-1000 text-nowrap">{{ $bet }} K</p>
                                    </div>
                                    <div class="grid grid-cols-[14px_66px_auto] items-center gap-1">
                                        <span class="w-[14px] h-[14px] bg-neutral-150 rounded-[4px]"></span>
                                        <div class="flex justify-between items-center w-full">
                                            <p class="text-[12px] leading-[18px] font-medium text-neutral-1000">Hoàn trả</p>
                                            <p class="text-[12px] leading-[18px] text-neutral-1000">:</p>
                                        </div>
                                        <p class="text-[12px] leading-[18px] text-neutral-1000 text-nowrap">
                                            {{ $return }} K
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </button>
                    @endforeach
                </div>
                <div class="flex items-center justify-center gap-5">
                    <div class="flex items-center gap-1">
                        <span class="w-4 h-4 bg-secondary-700 rounded-[4px]"></span>
                        <p class="text-[14px] leading-[20px] text-neutral-800">Tổng cược</p>
                    </div>
                    <div class="flex items-center gap-1">
                        <span class="w-4 h-4 bg-neutral-150 rounded-[4px]"></span>
                        <p class="text-[14px] leading-[20px] text-neutral-800">Hoàn trả</p>
                    </div>
                </div>
            </div>
            <div class="promotion-info grid grid-cols-2 gap-2 h-max xl:h-full xl:grid-cols-1">
                <div
                    class="promotion-info-item flex flex-col justify-between gap-[2px] py-[10px] px-2 rounded-[12px] bg-neutral-50 xl:flex-row xl:items-center xl:gap-[2px] xl:h-[60px] {{ $isOverview ? '' : 'xl:py-[15px] xl:px-[12px]'}}">
                    <p class="text-[10px] leading-[14px] font-medium text-neutral-800">TỔNG CƯỢC HÔM NAY</p>
                    <p class="font-semibold text-neutral-1000 text-[16px] leading-[24px]">
                        {{ $todayBet < 1 ? $todayBet : number_format((float) str_replace(',', '', $todayBet ?? 0), 0, '.', ',') }} K
                    </p>
                </div>
                <div
                    class="promotion-info-item flex flex-col justify-between gap-[2px] py-[10px] px-2 rounded-[12px] bg-neutral-50 xl:flex-row xl:items-center xl:gap-[2px] xl:h-[60px] {{ $isOverview ? 'xl:px-[16px]' : 'xl:py-[15px] xl:px-[12px]'}}">
                    <p class="text-[10px] leading-[14px] font-medium text-neutral-800">HOÀN TRẢ HÔM NAY</p>
                    <p class="font-semibold text-neutral-1000 text-[16px] leading-[24px]">
                        {{ $todayTotalReturn < 1 ? $todayTotalReturn : number_format($todayTotalReturn ?? 0, 0, '.', ',') }}
                        K</p>
                </div>
            </div>
        </div>
        <div
            class="promotion-container fixed top-0 left-0 z-[2147483648] hidden flex-col justify-end w-full h-dvh bg-black-50">
            <div
                class="promotion-detail-wrap relative flex flex-col items-start gap-4 pt-8 pb-8 px-5 bg-neutral rounded-t-[24px]">
                <button
                    class="promotion-close absolute top-[10.5px] right-[10px] z-[1] flex items-center justify-center w-[36px] h-[36px] bg-black-5 rounded-[4px] text-xl">
                    <img alt="close" src="{{ asset('asset/images/close.avif') }}"
                        class="w-[10px] h-[10px] object-cover" />
                </button>
                <p class="promotion-title text-[18px] leading-[26px] font-semibold text-neutral-1000 uppercase">Hoàn
                    trả ngày hôm nay</p>
                <div class="flex flex-col gap-2">
                    <div class="grid grid-cols-[14px_66px_auto] items-center gap-1">
                        <span class="w-[14px] h-[14px] bg-secondary-700 rounded-[4px]"></span>
                        <div class="flex justify-between items-center w-full">
                            <p class="text-[12px] leading-[18px] font-medium text-neutral-1000">Tổng cược</p>
                            <p class="text-[12px] leading-[18px] font-medium text-neutral-1000">:</p>
                        </div>
                        <p class="text-[12px] leading-[18px] text-neutral-1000"><span class="promotion-bet"></span>
                            K</p>
                    </div>
                    <div class="grid grid-cols-[14px_66px_auto] items-center gap-1">
                        <span class="w-[14px] h-[14px] bg-neutral-150 rounded-[4px]"></span>
                        <div class="flex justify-between items-center w-full">
                            <p class="text-[12px] leading-[18px] font-medium text-neutral-1000">Hoàn trả</p>
                            <p class="text-[12px] leading-[18px] font-medium text-neutral-1000">:</p>
                        </div>
                        <p class="text-[12px] leading-[18px] text-neutral-1000"><span class="promotion-refund"></span> K
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@else
    <div @class([
        'flex flex-col gap-4 w-full bg-neutral rounded-[24px] xl:gap-4',
        'xl:py-[24px] xl:px-[20px]' => !$noPadding,
    ])>
        <div class="promotion-header grid grid-cols-[100px_auto] gap-[12px] xl:grid-cols-[216px_auto] xl:gap-[16px]">
            <a href="{{ $data['link'] }}">
                <picture>
                    <source media="(min-width: 767px)" srcset="{{ asset($data['image-thumb']) }}">
                    <img src={{ asset($data['image-thumb-mb']) }}
                        class="promotion-banner w-full max-w-[100px] aspect-[100/80] rounded-[12px] xl:max-w-[216px] xl:aspect-[216/144]"
                        alt="promotion" />
                </picture>
            </a>
            <div class="flex flex-col justify-center">
                <p class="hidden text-[10px] leading-[14px] font-bold text-primary-700 uppercase xl:block">{{ $data['type'] ?? '' }}
                </p>
                 <a
                    href="{{ $data['link'] }}"
                    class="text-[14px] leading-[20px] font-semibold text-neutral-1000 xl:text-[16px] xl:leading-[24px] capitalize max-[391px]:w-[90%]"
                >
                    {{ $data['sub-title-detail'] ?? '' }}
                </a>
                <p class="text-xs leading-[18px] xl:text-sm text-neutral-800 {{$isOverview ? 'xl:w-[135px]': '' }}">{{ $data['sub-description-detail'] ?? '' }}</p>
            </div>
        </div>
        <div class="promotion-first-detail grid grid-cols-2 gap-[8px] xl:grid-cols-4">
            <div
                class="flex flex-col justify-between gap-[2px] py-[10px] px-[16px] rounded-[12px] bg-neutral-100 xl:py-[10px]">
                <p class="text-[10px] leading-[14px] font-medium text-neutral-800">SỐ TIỀN ĐÃ NẠP</p>
                <p class="text-[16px] leading-[24px] font-semibold text-neutral-1000 xl:text-[16px] xl:leading-[24px]">
                    {{ number_format(($accountInfo->deposit_amount ?? 0) / 1000, 0, '.', ',') }} K</p>
            </div>
            <div
                class="flex flex-col justify-between gap-[2px] py-[10px] px-[16px] rounded-[12px] bg-neutral-100 xl:py-[10px]">
                <p class="text-[10px] leading-[14px] font-medium text-neutral-800">SỐ TIỀN NHẬN ĐƯỢC</p>
                <p class="text-[16px] leading-[24px] font-semibold text-neutral-1000 xl:text-[16px] xl:leading-[24px]">
                    {{ number_format(($accountInfo->promotion_amount ?? 0) / 1000, 0, '.', ',') }} K</p>
            </div>
            <div
                class="flex flex-col justify-between gap-[2px] py-[10px] px-[16px] rounded-[12px] bg-neutral-100 xl:py-[10px]">
                <p class="text-[10px] leading-[14px] font-medium text-neutral-800">SỐ TIỀN ĐÃ CƯỢC</p>
                <p class="text-[16px] leading-[24px] font-semibold text-neutral-1000 xl:text-[16px] xl:leading-[24px]">
                    {{ number_format(($accountInfo->turnover ?? 0) / 1000, 0, '.', ',') }} K</p>
            </div>
            <div
                class="flex flex-col justify-between gap-[2px] py-[10px] px-[16px] rounded-[12px] bg-neutral-100 xl:py-[10px]">
                <p class="text-[10px] leading-[14px] font-medium text-neutral-800">
                    <span class="hidden xl:block">{{$isOverview ? "TỔNG CƯỢC CẦN ĐẠT" : "TỔNG CƯỢC CẦN HOÀN THÀNH"}}</span>
                <span class="inline-block xl:hidden">TỔNG CƯỢC CẦN ĐẠT</span></p>
                <p class="text-[16px] leading-[24px] font-semibold text-neutral-1000 xl:text-[16px] xl:leading-[24px]">
                    {{ number_format(($accountInfo->rolling ?? 0) / 1000, 0, '.', ',') }} K</p>
            </div>
        </div>
        <div class="flex flex-col gap-3">
            <div class="flex flex-col gap-[12px]">
                <div class="flex justify-between items-center">
                    <p class="text-[14px] leading-[20px] text-neutral-1000">Vòng cược</p>
                    <p class="text-[14px] leading-[20px] font-semibold text-primary-700">
                        {{ $currentMultiplier }}/{{ $accountInfo->multiplier }} Vòng</p>
                </div>
                <div class="w-full h-[16px] rounded-full bg-neutral-50">
                    <div class="h-full rounded-full bg-promotion-progress"
                        style="width:{{ $currentPercent }}%"></div>
                </div>
            </div>
            <div class="flex justify-between items-center">
                <div class="flex flex-col xl:flex-row xl:gap-[2px]">
                    <p class="text-[12px] leading-[18px] text-neutral-800 xl:text-[14px] xl:leading-[20px] normal-case">
                        Hạn dùng:
                    </p>
                    <p class="text-[12px] leading-[18px] text-neutral-800 xl:text-[14px] xl:leading-[20px]">
                        {{ $createdTime }} - {{ $endTime }}
                    </p>
                </div>
                <button 
                    onclick="cancelCurrentPromotion('{{$data['link']}}')"
                    class="min-w-max ml-auto text-[14px] leading-[20px] text-danger-600 cursor-pointer">
                    Hủy khuyến mãi
                </button>
            </div>
        </div>
    </div>
@endif

@pushOnce('scripts')
    @vite('resources/js/account/promotion-card.js')
@endPushOnce
