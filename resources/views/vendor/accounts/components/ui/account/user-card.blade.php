@php
    $overview = config('account.overview');
    $transaction = config('account.transaction');
    $more = config('account.more');
@endphp

<div class="menu-side bg-neutral rounded-2xl overflow-hidden min-h-[calc(100vh_-_176px)] xl:top-[142px] xl:min-w-[264px] xl:min-h-[663px]">
    @if (Auth::check())
    <div class="balance-content bg-center bg-no-repeat bg-cover w-full min-h-[99px] flex items-center"
    style="background-image: url('{{ asset('vendor/accounts/images/account/bg-wallet.avif') }}')"
    >
        <div class="flex flex-col pl-6">
            <p class="text-neutral-800 text-sm">{{__('account.overview.wallet.amount_title')}}</p>
            <p class="text-lg font-bold text-neutral-1000 leading-[calc(26/18)] js-account-balance"> {{ number_format(Auth::user()->balance ?? 0, 0, '.', ',') }} K</p>
        </div>
    </div>
    @endif
    <div class="w-full px-5 py-5 pt-[14px] xl:pt-[20px] max-[600px]:max-h-[700px]">
        <div class="pb-[14px] xl:pb-[11px] border-b border-solid border-b-neutral-150">
            @if (request()->url() === route('en.account.index'))

            <div class="xl:hidden">
                <a
                    href="{{route('en.account.index', ['tab' => 'overview']) }}"
                    class="menu-item-account relative flex justify-start items-center gap-x-2 p-3 cursor-pointer transition-all duration-300 ease-in "
                    >
                    <img src="{{ asset('vendor/accounts/images/account/menu/overview.svg') }}" alt="icon"
                    class="w-6 h-6 object-contain">
                    <span class="font-medium text-sm text-neutral-800 capitalize">
                        {{ __('account.menus.overview') }}
                    </span>
                </a>
            </div>

        <div class="hidden xl:block">
            <a
                href="{{route('en.account.index', ['tab' => 'overview']) }}"
                class="menu-item-account relative flex justify-start items-center gap-x-2 p-3 cursor-pointer transition-all duration-300 ease-in {{ request()->path() === ltrim($overview['href'], '/') ? 'active ' : '' }}"
                >
                <img src="{{ asset('vendor/accounts/images/account/menu/overview.svg') }}" alt="icon"
                class="w-6 h-6 object-contain {{ request()->path() === ltrim($overview['href'], '/') ? 'secondary-filter' : '' }}">
                <span class="font-medium text-sm text-neutral-800 capitalize">
                    {{ __('account.menus.overview') }}
                </span>
            </a>
        </div>


            @else
            <a href="{{ $overview['href'] }}"
                class="menu-item-account relative flex justify-start items-center gap-x-2 p-3 cursor-pointer transition-all duration-300 ease-in {{ request()->path() === ltrim($overview['href'], '/') ? 'active' : '' }}"
                data-href="{{ $overview['href'] }}">
                <img src="{{ asset('vendor/accounts/images/account/menu/overview.svg') }}" alt="icon"
                    class="w-6 h-6 object-contain {{ request()->path() === ltrim($overview['href'], '/') ? 'secondary-filter' : '' }}">
                <span class="font-medium text-sm text-neutral-800 capitalize">
                    {{ __('account.menus.overview') }}
                </span>
            </a>
                
            @endif
        </div>
        <div class="py-[14px] xl:py-[11px] border-b border-solid border-b-neutral-150">
            <div class="text-xs font-medium text-neutral-1000 capitalize">{{ __('account.menus.transaction_title') }}</div>
            <div class="mt-2 flex flex-col gap-y-1">
                @foreach ($transaction as $item)
                    @php
                        $active = request()->getPathInfo() === $item['href'] || in_array(request()->getPathInfo(), $item['active_list']);
                    @endphp
                <a href="{{ $item['href'] }}"
                    class="menu-item-account relative flex justify-start items-center gap-x-2 p-3 cursor-pointer transition-all duration-300 ease-in {{ $active ? 'active' : '' }}"
                    data-href="{{ $item['href'] }}">
                    <img src="{{ asset($item['icon']) }}" alt="icon" class="w-6 h-6 object-contain {{ $active ? 'secondary-filter' : '' }}">
                    <span class="font-medium text-sm text-neutral-800 capitalize tracking-[-0.5px]">
                        {{ __($item['name']) }}</span>
                </a>
                @endforeach
            </div>
        </div>
        <div class="py-[14px] xl:py-[11px] border-b border-solid border-b-neutral-150">
            <div class="text-xs font-medium text-neutral-1000 capitalize leading-[calc(18/12)]">{{ __('account.menus.more_title') }}</div>
            <div class="mt-2 flex flex-col gap-y-1">
                @foreach ($more as $item)
                <a href="{{ $item['href'] }}"
                    class="menu-item-account relative flex justify-start items-center gap-x-2 p-3 cursor-pointer transition-all duration-300 ease-in {{ request()->path() === ltrim($item['href'], '/') ? 'active' : '' }}"
                    data-href="{{ $item['href'] }}">
                    <img src="{{ asset($item['icon']) }}" alt="icon" class="w-6 h-6 object-contain {{ request()->path() === ltrim($item['href'], '/') ? 'secondary-filter' : '' }}">
                    <span class="font-medium text-sm text-neutral-800 capitalize"> {{ __($item['name']) }}</span>
                </a>
                @endforeach
            </div>
        </div>
        {{-- <div class="py-[12px] xl:py-[11px] border-b border-solid border-b-neutral-150 xl:hidden">
            <div class="mt-2 flex flex-col gap-y-1">
                <div class="js-language-dropdown flex items-center justify-between" data-target="#languageOptionAccount">
                    <div
                        class="menu-item-account relative flex justify-start items-center gap-x-2 p-3 cursor-pointer transition-all duration-300 ease-in"
                    >
                        <img src="{{ asset('asset/images/header/flag-' . Config::get('app.locale') . '.svg') }}" alt="icon" class="w-6 h-6 object-fill">
                        <span class="font-medium text-sm text-neutral-800 capitalize"> {{ Config::get('app.locale') === 'vi' ? __('header.menus.vietnamese') : __('header.menus.english') }}</span>
                </div>
                    <img src="{{ asset('asset/images/header/arrow-down.svg') }}" alt="arrow-down" class="js-language-arrow language-arrow w-6 h-6 mr-3" />
                </div>
                <div class="language__dropdown-content" id="languageOptionAccount">
                    <div
                        @class([
                            'language__dropdown-item flex items-center gap-2 h-10 rounded px-3 cursor-pointer',
                            'active-link bg-neutral-150' => Config::get('app.locale') === 'vi',
                        ])
                    >
                        <img src="{{ asset('asset/images/header/flag-vi.svg') }}" alt="flag-vi" class="w-6 h-6 object-fill" />
                        <a href="{{ route('change-language', ['lang' => 'vi']) }}" class="text-[12px] leading-[18px] font-medium text-sm text-neutral-1000">{{ __('header.menus.vietnamese') }}</a>
                    </div>
                    <div
                        @class([
                            'language__dropdown-item flex items-center gap-2 h-10 rounded px-3 cursor-pointer',
                            'active-link bg-neutral-150' => Config::get('app.locale') === 'en',
                        ])
                    >
                        <img src="{{ asset('asset/images/header/flag-en.svg') }}" alt="flag-en" class="w-6 h-6 object-fill" />
                        <a href="{{ route('change-language', ['lang' => 'en']) }}" class="text-[12px] leading-[18px] font-medium text-sm text-neutral-1000">{{ __('header.menus.english') }}</a>
                    </div>
                </div>
            </div>
        </div> --}}
        <div class="mt-[14px] xl:mt-3">
            <a onclick="openLogoutModal()"
                class="relative flex bg-neutral-50 justify-center items-center gap-x-1 w-full h-12 cursor-pointer rounded-lg">
                <img src="{{ asset('vendor/accounts/images/account/menu/logout.svg') }}" alt="icon" class="w-6 h-auto aspect-square">
                <span class="font-medium text-sm text-neutral-800 text-center capitalize">
                    {{ __('auth.logout') }}
                </span>
            </a>
        </div>
    </div>
</div>
