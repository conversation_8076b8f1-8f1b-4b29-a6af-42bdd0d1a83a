<?php

use App\Http\Controllers\AboutUsController\AboutUsController;
use App\Http\Controllers\AuthController\AuthController;
use App\Http\Controllers\CasinoController\CasinoController;
use App\Http\Controllers\DisclaimerController\DisclaimerController;
use App\Http\Controllers\EventsController\EventsController;
use App\Http\Controllers\GameController\GameController;
use App\Http\Controllers\HelpController\HelpController;
use App\Http\Controllers\HomeController\HomeController;
use App\Http\Controllers\IframeSportsController\IframeSportsController;
use App\Http\Controllers\InstructionController\InstructionController;
use App\Http\Controllers\NewsController\NewsController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\PolicyController\PolicyController;
use App\Http\Controllers\QuestionController\QuestionController;
use App\Http\Controllers\SitemapController\SitemapController;
use App\Http\Controllers\SportsController\SportsController;
use App\Http\Controllers\TermsController\TermsController;
use App\Http\Middleware\JackpotMiddleware;
use App\Http\Middleware\DetectGameAuth;
use App\Http\Controllers\CommonController\ChangeLocaleController;
use Illuminate\Support\Facades\Route;

// To ensure that both the prefixed and non-prefixed routes work correctly, you need to explicitly define routes that do not require the prefix.
// Detect cookie then redirects to set language. Default is en
Route::get('/', [HomeController::class, 'index'])->middleware(JackpotMiddleware::class)->name('en.home.index');
Route::get('/refresh', [AuthController::class, 'refresh'])->name('en.refresh.index');

// Route::get('/login', [AuthController::class, 'index'])->name('en.login.index');
// Route::get('/signup', [AuthController::class, 'signup'])->name('en.login.signup');
Route::get('/cong-game', [GameController::class, 'index'])->middleware(JackpotMiddleware::class)->name('en.games.index');
Route::get('/cong-game/{slug}', [GameController::class, 'index'])->middleware(JackpotMiddleware::class)->name('en.games.type');
Route::get('/ca-do-bong-da', [SportsController::class, 'index'])->name('en.sports.index');
Route::get('/song-bai-livecasino-truc-tuyen', [CasinoController::class, 'index'])->middleware(JackpotMiddleware::class)->name('en.casino.index');
Route::get('/song-bai-livecasino-truc-tuyen/{slug}', [CasinoController::class, 'index'])->middleware(JackpotMiddleware::class)->name('en.casino.type');
// Route::get('/events', [EventsController::class, 'index'])->name('en.events.index');
// Route::get('/event/{slug}', [EventsController::class, 'event'])->name('en.events.event');
// Route::get('/promo/{slug}', [EventsController::class, 'promo'])->name('en.events.promo');
Route::get('/lixi/newyear', [EventsController::class, 'rewardGet'])->name('en.reward-get.index');
Route::post('/lixi/newyear', [EventsController::class, 'rewardPost'])->name('en.reward-post.index');
Route::get('/top-racing', [EventsController::class, 'rankList'])->name('en.rank-list.index');
Route::get('/popup', [EventsController::class, 'popup'])->name('en.popup.index');
Route::post('/popup/close/{id}', [EventsController::class, 'popupClose'])->name('en.popup-close.index');
Route::get('/home/<USER>', [EventsController::class, 'timeServer'])->name('en.time-server.index');
// Route::get('/tin-tuc', [NewsController::class, 'index'])->name('en.news.index');
// Route::get('/tin-tuc/post/{slug}', [NewsController::class, 'newsDetail'])->name('en.news.detail');
// Route::get('/tin-tuc/{slug}', [NewsController::class, 'category'])->name('en.news.category');
Route::get('/chinh-sach-bao-mat', [PolicyController::class, 'getPolicy'])->name('en.policy.index');
Route::get('/dieu-khoan-dieu-kien', [TermsController::class, 'index'])->name('en.terms.index');
Route::get('/ve-chung-toi', [AboutUsController::class, 'index'])->name('en.about-us.index');
Route::get('/tro-giup', [HelpController::class, 'index'])->name('en.help.index');
Route::get('/cau-hoi-thuong-gap', [QuestionController::class, 'index'])->name('en.question.index');
Route::get('/mien-trach-nhiem', [DisclaimerController::class, 'index'])->name('en.disclaimer.index');
// Route::get('/huong-dan-dang-ky', [InstructionController::class, 'register'])->name('en.instruction.index');
// Route::get('/huong-dan-nap-tien', [InstructionController::class, 'deposit'])->name('en.instruction.deposit');
// Route::get('/huong-dan-rut-tien', [InstructionController::class, 'withdrawal'])->name('en.instruction.withdrawal');
// Route::get('/huong-dan-giao-dich-p2p', [InstructionController::class, 'p2p'])->name('en.instruction.p2p');
Route::get('/gameUrl', [GameController::class, 'gameUrl'])->name('en.gameUrl.index');
Route::get('/casinoUrl', [CasinoController::class, 'casinoUrl'])->name('en.casinoUrl.index');
Route::group(['prefix' => 'daga'], function () {
    Route::get('/{slug}', [GameController::class, 'cockfight'])->middleware(DetectGameAuth::class)->name('en.cockfight.index');
});
Route::get('/lo-de-sieu-toc', [GameController::class, 'getLoDeSieuToc'])->middleware(DetectGameAuth::class)->name('en.lode.sieu-toc');
Route::get('/lode-3-mien', [GameController::class, 'getLode3mien'])->middleware(DetectGameAuth::class)->name('en.lode.index');
Route::get('/lode', [GameController::class, 'getListLode'])->middleware(DetectGameAuth::class)->name('en.lode-list.index');
Route::get('/mega645', [GameController::class, 'getMega645'])->middleware(DetectGameAuth::class)->name('en.mega645.index');
Route::get('/power655', [GameController::class, 'getPower655'])->middleware(DetectGameAuth::class)->name('en.power655.index');
Route::get('/md5', [GameController::class, 'getMd5'])->middleware(DetectGameAuth::class)->name('en.md5.index');
Route::get('/daga', [GameController::class, 'getListDaga'])->name('en.daga-list.index');
Route::get('/quayso', [GameController::class, 'getQuaySoList'])->name('en.quayso-list.index');
Route::group(['prefix' => 'quayso'], function () {
    Route::get('/{slug}', [GameController::class, 'getQuaySo'])->middleware(DetectGameAuth::class)->name('en.quayso.index');
});
Route::get('/sitemap.xml', [SitemapController::class, 'index'])->name('en.robot.sitemap');
Route::get('/general.xml', [SitemapController::class, 'general'])->name('en.robot.general');
Route::get('/games.xml', [SitemapController::class, 'games'])->name('en.robot.games');
Route::get('/events.xml', [SitemapController::class, 'events'])->name('en.robot.events');
Route::get('/huongdan.xml', [SitemapController::class, 'guide'])->name('en.robot.guide');
Route::get('/theloai.xml', [SitemapController::class, 'category'])->name('en.robot.category');
Route::get('/articles.xml', [SitemapController::class, 'articles'])->name('en.robot.articles');

Route::post('/login', [AuthController::class, 'handleLogin'])->name('auth.login');
Route::post('/register', [AuthController::class, 'handleRegister'])->name('auth.register');
Route::get('/logout', [AuthController::class, 'handleLogout'])->name('auth.logout');
Route::post('/change-pass', [AuthController::class, 'handleChangePass'])->name('en.change-pass.index');

// Sports routes group
Route::prefix('ca-do-bong-da')->group(function () {
    Route::get('{gameType}', [IframeSportsController::class, 'handleSports'])
        ->middleware(DetectGameAuth::class)
        ->name('en.iframe-sports');
});

Route::get('/notification', [NotificationController::class, 'index'])->name('en.notification.index');

Route::get('/change-language/{lang}', [ChangeLocaleController::class, 'changeLocale'])->name('change-language');
// JavaScript translations
Route::get('/js/translations/{locale}', [ChangeLocaleController::class, 'getTranslations'])->name('js.translations');