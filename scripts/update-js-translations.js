#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to automatically update JavaScript files to use translation system
 * This script will scan JS files and replace hardcoded Vietnamese text with translation calls
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Common Vietnamese text patterns and their translation keys
const translationMappings = {
    // Validation messages
    'Vui lòng nhập': 'required',
    'không đúng định dạng': 'invalid_format',
    'phải từ': 'length_requirement',
    'ký tự': 'characters',
    
    // Success/Error messages
    'thành công': 'success',
    'thất bại': 'failed',
    'Đã có lỗi': 'error_occurred',
    'vui lòng thử lại': 'please_try_again',
    
    // Common actions
    'Đóng': 'close',
    'Hủy': 'cancel',
    'Xác nhận': 'confirm',
    'Thử lại': 'try_again',
    '<PERSON><PERSON><PERSON> hệ <PERSON>': 'contact_support',
    '<PERSON><PERSON><PERSON> nhập': 'login',
    '<PERSON><PERSON><PERSON> ký': 'register',
    
    // Account related
    'Tài khoản': 'account',
    '<PERSON><PERSON><PERSON> khẩu': 'password',
    '<PERSON><PERSON> điện thoại': 'phone',
    'Email': 'email',
    'Thông tin': 'information',
    
    // Transaction related
    'Nạp tiền': 'deposit',
    'Rút tiền': 'withdraw',
    'Giao dịch': 'transaction',
    'Lịch sử': 'history',
    'Số dư': 'balance',
    
    // Notifications
    'Thông báo': 'notification',
    'Cảnh báo': 'warning',
    'Lỗi': 'error',
    'Thành công': 'success'
};

// Specific text replacements for common phrases
const specificReplacements = {
    // Auth messages
    'Vui lòng nhập tên đăng nhập': '__("auth.username_required")',
    'Vui lòng nhập mật khẩu': '__("auth.password_required")',
    'Vui lòng nhập số điện thoại': '__("auth.phone_required")',
    'Vui lòng nhập email': '__("auth.email_required")',
    'Tên đăng nhập từ 6 đến 29 ký tự': '__("auth.username_length")',
    'Mật khẩu từ 6 đến 32 ký tự': '__("auth.password_length")',
    'Địa chỉ email không hợp lệ': '__("auth.email_invalid")',
    'Đăng ký tài khoản thành công': '__("auth.register_success")',
    'Cập nhật mật khẩu thành công': '__("auth.password_update_success")',
    
    // Common actions
    'Đóng': '__("pages.account.close")',
    'Hủy': '__("common.cancel")',
    'Xác nhận': '__("pages.account.confirm")',
    'Thử lại': '__("pages.account.try_again")',
    'Liên hệ CSKH': '__("pages.account.contact_support")',
    
    // Deposit messages
    'Nạp tiền thành công': '__("pages.account.deposit_success")',
    'Nạp tiền thất bại': '__("pages.account.deposit_failed")',
    'Tạo phiếu nạp thành công': '__("pages.account.deposit_success")',
    'Đã có lỗi trong quá trình giao dịch': '__("pages.account.transaction_error")',
    
    // Promotion messages
    'Hủy khuyến mãi thành công': '__("pages.account.cancel_promotion_success")',
    'Hủy khuyến mãi thất bại': '__("pages.account.cancel_promotion_failed")',
    'Xem khuyến mãi': '__("pages.account.view_promotion")',
    
    // Session messages
    'Phiên đăng nhập hết hạn': '__("pages.account.session_expired")',
    'Vui lòng đăng nhập lại để tiếp tục trải nghiệm': '__("pages.account.session_expired_message")'
};

function updateJSFile(filePath) {
    console.log(`Processing: ${filePath}`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Apply specific replacements
    for (const [original, replacement] of Object.entries(specificReplacements)) {
        const regex = new RegExp(`["']${original.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}["']`, 'g');
        if (content.match(regex)) {
            content = content.replace(regex, replacement);
            modified = true;
            console.log(`  - Replaced: "${original}" -> ${replacement}`);
        }
    }
    
    // Save the file if modified
    if (modified) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`  ✓ Updated: ${filePath}`);
    } else {
        console.log(`  - No changes needed: ${filePath}`);
    }
    
    return modified;
}

function main() {
    console.log('🚀 Starting JavaScript translation update...\n');
    
    // Find all JS files in resources/js
    const jsFiles = glob.sync('resources/js/**/*.js', {
        ignore: [
            'resources/js/translation.js',
            'resources/js/translation-demo.js'
        ]
    });
    
    console.log(`Found ${jsFiles.length} JavaScript files to process\n`);
    
    let totalModified = 0;
    
    jsFiles.forEach(file => {
        if (updateJSFile(file)) {
            totalModified++;
        }
        console.log(''); // Empty line for readability
    });
    
    console.log(`\n✅ Completed! Modified ${totalModified} out of ${jsFiles.length} files.`);
    console.log('\n📝 Next steps:');
    console.log('1. Review the changes in each file');
    console.log('2. Add any missing translation keys to language files');
    console.log('3. Test the translations in the browser');
    console.log('4. Run: npm run build to compile the assets');
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = { updateJSFile, translationMappings, specificReplacements };
