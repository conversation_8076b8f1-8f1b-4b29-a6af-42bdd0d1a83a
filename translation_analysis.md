# Phân tích Text cần đa ngôn ngữ hóa

## C<PERSON>c text hardcode đã tìm thấy:

### 1. resources/views/pages/help.blade.php
- "Live Casino" (line 6)
- "Khám phá thế giới giải trí đỉnh cao cùng" (line 8)

### 2. resources/views/components/ui/home/<USER>
- "Hướng dẫn" (line 2) - c<PERSON> thể tách thành "Hướng" và "dẫn"
- "Hướng dẫn nạp tiền" (line 10)
- "Hướng dẫn rút tiền" (line 23)
- "Xem Thêm" (line 30)

### 3. resources/views/components/ui/home/<USER>
- "Tin tức" (line 8) - c<PERSON> thể tách thành "Tin" và "tức"
- "Không có dữ liệu" (line 32)

### 4. resources/views/components/ui/auth/login-form.blade.php
- "Nhà cái trực tuyến hàng đầu" (line 23, 33)
- "<PERSON><PERSON>ng <PERSON>ập" (line 37)
- "<PERSON><PERSON><PERSON> quý khách hàng may mắn" (line 38)
- "Tên đăng nhập" (line 44)

## Kế hoạch tổ chức file ngôn ngữ:

### File lang/vi/pages.php (mới)
```php
<?php
return [
    'help' => [
        'live_casino' => 'Live Casino',
        'explore_entertainment' => 'Khám phá thế giới giải trí đỉnh cao cùng :brandName',
    ],
    'home' => [
        'instruction_title' => 'Hướng dẫn',
        'instruction_deposit' => 'Hướng dẫn nạp tiền',
        'instruction_withdraw' => 'Hướng dẫn rút tiền',
        'see_more' => 'Xem Thêm',
        'news_title' => 'Tin tức',
        'no_data' => 'Không có dữ liệu',
    ],
    'auth' => [
        'casino_slogan' => 'Nhà cái trực tuyến hàng đầu',
        'login_title' => 'Đăng Nhập',
        'login_greeting' => 'Chúc quý khách hàng may mắn',
        'username_placeholder' => 'Tên đăng nhập',
    ],
];
```

### File lang/en/pages.php (mới)
```php
<?php
return [
    'help' => [
        'live_casino' => 'Live Casino',
        'explore_entertainment' => 'Explore the pinnacle of entertainment with :brandName',
    ],
    'home' => [
        'instruction_title' => 'Instructions',
        'instruction_deposit' => 'Deposit Guide',
        'instruction_withdraw' => 'Withdrawal Guide',
        'see_more' => 'See More',
        'news_title' => 'News',
        'no_data' => 'No data available',
    ],
    'auth' => [
        'casino_slogan' => 'Leading online casino',
        'login_title' => 'Login',
        'login_greeting' => 'Wishing our customers good luck',
        'username_placeholder' => 'Username',
    ],
];
```

## Các file cần cập nhật:
1. resources/views/pages/help.blade.php
2. resources/views/components/ui/home/<USER>
3. resources/views/components/ui/home/<USER>
4. resources/views/components/ui/auth/login-form.blade.php

## Pattern thay thế:
- Text hardcode → __('pages.section.key')
- Text có biến → __('pages.section.key', ['brandName' => env('BRAND_NAME')])
